APP_NAME=ClockIn
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://votre-domaine.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Configuration base de données production
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=clockin_production
DB_USERNAME=clockin_user
DB_PASSWORD=mot_de_passe_securise

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.votre-domaine.com

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Configuration email production
MAIL_MAILER=smtp
MAIL_HOST=smtp.votre-domaine.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mot_de_passe_email
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Configuration Sanctum pour production
SANCTUM_STATEFUL_DOMAINS=votre-domaine.com,app.votre-domaine.com

# Configuration CORS pour production
CORS_ALLOWED_ORIGINS=https://votre-domaine.com,https://app.votre-domaine.com

# Configuration SSL
FORCE_HTTPS=true

# Configuration de sécurité
SECURE_COOKIES=true
SAME_SITE_COOKIES=strict

# Configuration de cache
CACHE_PREFIX=clockin_prod

# Configuration des logs
LOG_DAILY_DAYS=14
LOG_MAX_FILES=30

# Configuration de la base de données pour les sessions
SESSION_CONNECTION=mysql
SESSION_TABLE=sessions

# Configuration de la base de données pour le cache
CACHE_CONNECTION=mysql
CACHE_TABLE=cache

# Configuration de la base de données pour les jobs
QUEUE_CONNECTION=database
QUEUE_TABLE=jobs
QUEUE_FAILED_TABLE=failed_jobs
