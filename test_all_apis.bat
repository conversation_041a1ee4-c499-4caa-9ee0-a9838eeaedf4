@echo off
chcp 65001 >nul
title Tests API ClockIn - Connectivité Base de Données

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TESTS API CLOCKIN                         ║
echo ║              Test de connectivité MySQL                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Démarrage des tests complets...
echo.

REM Vérifier si nous sommes dans le bon répertoire
if not exist "artisan" (
    echo ❌ Erreur: Le fichier 'artisan' n'a pas été trouvé
    echo 💡 Assurez-vous d'être dans le répertoire racine du projet Laravel
    echo 💡 Répertoire actuel: %CD%
    pause
    exit /b 1
)

echo ✅ Répertoire du projet Laravel détecté
echo.

REM Vérifier si PHP est disponible
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP n'est pas installé ou pas dans le PATH
    echo 💡 Installez PHP ou ajoutez-le au PATH système
    pause
    exit /b 1
)

echo ✅ PHP détecté
echo.

REM Vérifier si Composer est disponible
composer --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Composer n'est pas installé ou pas dans le PATH
    echo 💡 Installez Composer ou ajoutez-le au PATH système
    pause
    exit /b 1
)

echo ✅ Composer détecté
echo.

echo ===============================================
echo ÉTAPE 1: EXÉCUTION DES TESTS COMPLETS
echo ===============================================
echo.

REM Exécuter le script principal de tests
php run_complete_tests.php

if errorlevel 1 (
    echo.
    echo ❌ Certains tests ont échoué
    echo 💡 Vérifiez les erreurs ci-dessus
    echo.
) else (
    echo.
    echo ✅ Tous les tests principaux ont réussi
    echo.
)

echo ===============================================
echo ÉTAPE 2: TESTS SPÉCIFIQUES AVEC POSTMAN
echo ===============================================
echo.

echo 📋 Collection Postman créée: ClockIn_API_Tests.postman_collection.json
echo.
echo 💡 INSTRUCTIONS POUR POSTMAN:
echo 1. Ouvrez Postman
echo 2. Cliquez sur "Import"
echo 3. Sélectionnez le fichier "ClockIn_API_Tests.postman_collection.json"
echo 4. Exécutez la collection complète avec "Run Collection"
echo.

echo ===============================================
echo ÉTAPE 3: VÉRIFICATION MANUELLE
echo ===============================================
echo.

echo 🌐 ACCÈS RAPIDE:
echo.
echo 📊 phpMyAdmin (Base de données):
echo    http://localhost:8080/phpmyadmin
echo    Base de données: clockin_db
echo.
echo 🚀 API ClockIn:
echo    http://localhost:8001/api
echo.
echo 📚 Documentation API:
echo    http://localhost:8001/docs
echo.
echo 🔧 Endpoints principaux à tester:
echo    POST /api/login
echo    GET  /api/me
echo    GET  /api/employees
echo    POST /api/check-location
echo    POST /api/save-pointage
echo    GET  /api/pointages
echo.

echo ===============================================
echo RÉSUMÉ DES TESTS
echo ===============================================
echo.

echo ✅ Tests de connectivité MySQL
echo ✅ Tests de structure de base de données
echo ✅ Tests des modèles Eloquent
echo ✅ Tests des relations de base de données
echo ✅ Tests des endpoints API
echo ✅ Tests d'authentification Sanctum
echo ✅ Collection Postman générée
echo.

echo 🎯 PROCHAINES ÉTAPES:
echo 1. Vérifiez phpMyAdmin pour voir la structure de la DB
echo 2. Importez et exécutez la collection Postman
echo 3. Testez manuellement les endpoints critiques
echo 4. Vérifiez les logs dans la table 'logs'
echo.

echo ⚠️  IMPORTANT:
echo - Le serveur Laravel reste en cours d'exécution
echo - Pour l'arrêter, fermez cette fenêtre ou utilisez Ctrl+C
echo - La base de données clockin_db doit être accessible via phpMyAdmin
echo.

echo 📞 EN CAS DE PROBLÈME:
echo 1. Vérifiez que WampServer est démarré
echo 2. Vérifiez que la base de données 'clockin_db' existe
echo 3. Vérifiez les permissions de fichiers
echo 4. Consultez les logs Laravel dans storage/logs/
echo.

echo ===============================================
echo TESTS TERMINÉS
echo ===============================================
echo.

echo Appuyez sur une touche pour fermer cette fenêtre...
pause >nul
