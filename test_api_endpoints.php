<?php

echo "=== TEST DES ENDPOINTS API CLOCKIN ===\n\n";

$baseUrl = 'http://localhost:8001/api';
$testResults = [];

// Fonction pour faire des requêtes HTTP
function makeApiRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error,
        'success' => !$error && $httpCode < 400
    ];
}

// Fonction pour afficher les résultats
function displayResult($testName, $result, $expectedCode = 200) {
    global $testResults;
    
    $success = $result['success'] && $result['http_code'] == $expectedCode;
    $testResults[] = $success;
    
    $status = $success ? "✅" : "❌";
    echo "$status $testName\n";
    echo "   Code HTTP: {$result['http_code']}\n";
    
    if ($result['error']) {
        echo "   Erreur: {$result['error']}\n";
    }
    
    if ($result['response']) {
        $response = json_decode($result['response'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($response['success'])) {
                echo "   Succès API: " . ($response['success'] ? 'true' : 'false') . "\n";
            }
            if (isset($response['message'])) {
                $message = is_array($response['message']) ? $response['message']['fr'] ?? $response['message']['en'] ?? 'Message' : $response['message'];
                echo "   Message: $message\n";
            }
        }
    }
    echo "\n";
    
    return $success;
}

echo "1. VÉRIFICATION DE LA CONNECTIVITÉ DU SERVEUR\n";
echo "==============================================\n";

// Test de base - vérifier si le serveur répond
$result = makeApiRequest($baseUrl . '/login', 'POST', ['email' => 'test', 'password' => 'test']);
if ($result['error']) {
    echo "❌ Serveur Laravel non accessible\n";
    echo "Erreur: {$result['error']}\n";
    echo "\n💡 ACTIONS REQUISES:\n";
    echo "1. Démarrez le serveur Laravel: php artisan serve --port=8001\n";
    echo "2. Vérifiez que le port 8001 est libre\n";
    echo "3. Vérifiez la configuration du serveur\n";
    exit(1);
} else {
    echo "✅ Serveur Laravel accessible\n\n";
}

echo "2. TESTS D'AUTHENTIFICATION\n";
echo "============================\n";

// Test de login avec identifiants invalides
$result = makeApiRequest($baseUrl . '/login', 'POST', [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
]);
displayResult("Login avec identifiants invalides", $result, 401);

// Test de login avec données manquantes
$result = makeApiRequest($baseUrl . '/login', 'POST', [
    'email' => 'invalid-email'
]);
displayResult("Login avec données invalides", $result, 422);

// Test de login avec identifiants valides (admin par défaut)
$result = makeApiRequest($baseUrl . '/login', 'POST', [
    'email' => '<EMAIL>',
    'password' => 'password123'
]);
$loginSuccess = displayResult("Login avec identifiants valides", $result, 200);

$adminToken = null;
if ($loginSuccess && $result['response']) {
    $response = json_decode($result['response'], true);
    if (isset($response['data']['token'])) {
        $adminToken = $response['data']['token'];
        echo "🔑 Token admin récupéré\n\n";
    }
}

if (!$adminToken) {
    echo "❌ Impossible de récupérer le token admin\n";
    echo "💡 Vérifiez que les seeders ont été exécutés: php artisan db:seed\n\n";
}

echo "3. TESTS DES ROUTES PROTÉGÉES\n";
echo "==============================\n";

// Test d'accès sans token
$result = makeApiRequest($baseUrl . '/me', 'GET');
displayResult("Accès au profil sans token", $result, 401);

if ($adminToken) {
    // Test d'accès avec token valide
    $result = makeApiRequest($baseUrl . '/me', 'GET', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Accès au profil avec token valide", $result, 200);
    
    echo "4. TESTS DE GESTION DES EMPLOYÉS (ADMIN)\n";
    echo "=========================================\n";
    
    // Liste des employés
    $result = makeApiRequest($baseUrl . '/employees', 'GET', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Liste des employés", $result, 200);
    
    // Création d'un employé
    $newEmployee = [
        'name' => 'Test Employee API',
        'email' => 'test_employee_' . time() . '@test.com',
        'password' => 'password123',
        'role' => 'employee'
    ];
    
    $result = makeApiRequest($baseUrl . '/employees', 'POST', $newEmployee, [
        'Authorization: Bearer ' . $adminToken
    ]);
    $createSuccess = displayResult("Création d'un employé", $result, 201);
    
    $newEmployeeId = null;
    if ($createSuccess && $result['response']) {
        $response = json_decode($result['response'], true);
        if (isset($response['data']['id'])) {
            $newEmployeeId = $response['data']['id'];
        }
    }
    
    if ($newEmployeeId) {
        // Consultation de l'employé
        $result = makeApiRequest($baseUrl . "/employees/$newEmployeeId", 'GET', null, [
            'Authorization: Bearer ' . $adminToken
        ]);
        displayResult("Consultation d'un employé", $result, 200);
        
        // Modification de l'employé
        $updateData = [
            'name' => 'Test Employee Modified',
            'email' => $newEmployee['email'],
            'role' => 'employee'
        ];
        
        $result = makeApiRequest($baseUrl . "/employees/$newEmployeeId", 'PUT', $updateData, [
            'Authorization: Bearer ' . $adminToken
        ]);
        displayResult("Modification d'un employé", $result, 200);
        
        // Suppression de l'employé
        $result = makeApiRequest($baseUrl . "/employees/$newEmployeeId", 'DELETE', null, [
            'Authorization: Bearer ' . $adminToken
        ]);
        displayResult("Suppression d'un employé", $result, 200);
    }
    
    echo "5. TESTS DE GESTION DES SITES\n";
    echo "==============================\n";
    
    // Liste des sites
    $result = makeApiRequest($baseUrl . '/sites', 'GET', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Liste des sites", $result, 200);
    
    // Création d'un site
    $newSite = [
        'name' => 'Site Test API',
        'latitude' => 34.0209,
        'longitude' => -6.8416
    ];
    
    $result = makeApiRequest($baseUrl . '/sites', 'POST', $newSite, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Création d'un site", $result, 201);
    
    echo "6. TESTS DE POINTAGE\n";
    echo "====================\n";
    
    // Test de vérification de localisation
    $result = makeApiRequest($baseUrl . '/check-location', 'POST', [
        'site_id' => 1,
        'latitude' => 33.5731,
        'longitude' => -7.5898
    ], [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Vérification de localisation", $result, 200);
    
    // Test de vérification avec coordonnées invalides
    $result = makeApiRequest($baseUrl . '/check-location', 'POST', [
        'site_id' => 1,
        'latitude' => 91, // Invalide
        'longitude' => 181 // Invalide
    ], [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Vérification avec coordonnées invalides", $result, 422);
    
    // Liste des pointages (admin)
    $result = makeApiRequest($baseUrl . '/pointages', 'GET', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Liste des pointages", $result, 200);
    
    echo "7. TESTS DE VÉRIFICATION\n";
    echo "=========================\n";
    
    // Historique des vérifications
    $result = makeApiRequest($baseUrl . '/verifications', 'GET', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Historique des vérifications", $result, 200);
    
    // Logout
    $result = makeApiRequest($baseUrl . '/logout', 'POST', null, [
        'Authorization: Bearer ' . $adminToken
    ]);
    displayResult("Logout", $result, 200);
}

echo "8. RÉSUMÉ DES TESTS\n";
echo "===================\n";

$totalTests = count($testResults);
$passedTests = array_sum($testResults);

echo "Tests exécutés: $totalTests\n";
echo "Tests réussis: $passedTests\n";
echo "Tests échoués: " . ($totalTests - $passedTests) . "\n";
echo "Taux de réussite: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 TOUS LES TESTS API SONT RÉUSSIS !\n";
    echo "✅ Toutes les APIs fonctionnent correctement\n";
    echo "✅ La base de données est correctement intégrée\n";
    echo "✅ L'authentification et les autorisations fonctionnent\n";
    echo "✅ Les validations de données sont opérationnelles\n";
} else {
    echo "⚠️  CERTAINS TESTS API ONT ÉCHOUÉ\n";
    echo "Vérifiez les erreurs ci-dessus\n";
    
    if (!$adminToken) {
        echo "\n💡 PROBLÈME PRINCIPAL: Token admin non récupéré\n";
        echo "Actions recommandées:\n";
        echo "1. Vérifiez que la base de données est configurée\n";
        echo "2. Exécutez les migrations: php artisan migrate\n";
        echo "3. Exécutez les seeders: php artisan db:seed\n";
        echo "4. Vérifiez les identifiants par défaut\n";
    }
}

echo "\n=== FIN DES TESTS API ===\n";
