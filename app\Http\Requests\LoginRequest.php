<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.required' => 'Email is required / Email requis / البريد الإلكتروني مطلوب',
            'email.email' => 'Invalid email format / Format email invalide / تنسيق البريد الإلكتروني غير صحيح',
            'password.required' => 'Password is required / Mot de passe requis / كلمة المرور مطلوبة',
            'password.min' => 'Password must be at least 6 characters / Le mot de passe doit contenir au moins 6 caractères / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Validation failed',
                    'fr' => 'Échec de la validation',
                    'ar' => 'فشل التحقق'
                ],
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
