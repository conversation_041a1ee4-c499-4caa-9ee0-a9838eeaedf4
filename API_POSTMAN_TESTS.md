# 🧪 Tests API Postman - ClockIn System

Ce guide présente tous les tests API effectués pour valider le bon fonctionnement du système ClockIn avec une approche similaire à Postman.

## 🔧 Configuration des Tests

**Base URL**: `http://localhost:8001/api`

**Headers requis**:
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}" // Pour les routes protégées
}
```

## 📊 Plan de Tests

### Phase 1: Tests d'Authentification
1. ✅ POST /api/login - Connexion admin
2. ✅ GET /api/me - Profil utilisateur
3. ✅ POST /api/logout - Déconnexion

### Phase 2: Tests Gestion Employés (Admin)
4. ✅ GET /api/employees - Liste employés
5. ✅ POST /api/employees - Créer employé
6. ✅ GET /api/employees/{id} - Détails employé
7. ✅ PUT /api/employees/{id} - Modifier employé
8. ✅ DELETE /api/employees/{id} - Supprimer employé

### Phase 3: Tests Gestion Sites (Admin)
9. ✅ GET /api/sites - Liste sites
10. ✅ POST /api/sites - Créer site
11. ✅ POST /api/assign-site - Assigner site
12. ✅ GET /api/my-sites - Sites assignés (Employé)

### Phase 4: Tests Pointage
13. ✅ POST /api/check-location - Vérifier position
14. ✅ POST /api/save-pointage - Enregistrer pointage
15. ✅ GET /api/pointages - Liste pointages (Admin)

### Phase 5: Tests Vérifications
16. ✅ POST /api/request-verification - Demander vérification
17. ✅ POST /api/verify-location - Vérifier position
18. ✅ GET /api/verifications - Historique vérifications

## 🔍 Détails des Tests

### 🔐 Phase 1: Tests d'Authentification

#### Test 1: POST /api/login ✅
**Objectif** : Connexion utilisateur admin
**Méthode** : POST
**URL** : `http://localhost:8001/api/login`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json"
}
```
**Payload** :
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Login successful",
        "fr": "Connexion réussie",
        "ar": "تم تسجيل الدخول بنجاح"
    },
    "data": {
        "user": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>",
            "role": "admin"
        },
        "token": "1|abc123...",
        "token_type": "Bearer"
    }
}
```

#### Test 2: GET /api/me ✅
**Objectif** : Récupérer profil utilisateur connecté
**Méthode** : GET
**URL** : `http://localhost:8001/api/me`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token_from_login}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>",
        "role": "admin"
    }
}
```

#### Test 3: POST /api/logout ✅
**Objectif** : Déconnexion utilisateur
**Méthode** : POST
**URL** : `http://localhost:8001/api/logout`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token_from_login}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Logout successful",
        "fr": "Déconnexion réussie",
        "ar": "تم تسجيل الخروج بنجاح"
    }
}
```

### 👥 Phase 2: Tests Gestion Employés (Admin)

#### Test 4: GET /api/employees ✅
**Objectif** : Récupérer liste des employés
**Méthode** : GET
**URL** : `http://localhost:8001/api/employees`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Paramètres optionnels** :
- `?search=john` - Recherche par nom/email
- `?role=employee` - Filtrer par rôle
- `?page=1&per_page=10` - Pagination

**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": [
        {
            "id": 2,
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "employee",
            "created_at": "2024-01-01T00:00:00.000000Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "total": 5,
        "per_page": 10
    }
}
```

#### Test 5: POST /api/employees ✅
**Objectif** : Créer un nouvel employé
**Méthode** : POST
**URL** : `http://localhost:8001/api/employees`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Payload** :
```json
{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "employee"
}
```
**Réponse attendue** : 201 Created
```json
{
    "success": true,
    "message": {
        "en": "Employee created successfully",
        "fr": "Employé créé avec succès",
        "ar": "تم إنشاء الموظف بنجاح"
    },
    "data": {
        "id": 3,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "role": "employee",
        "created_at": "2024-01-01T00:00:00.000000Z"
    }
}
```

#### Test 6: GET /api/employees/{id} ✅
**Objectif** : Récupérer détails d'un employé
**Méthode** : GET
**URL** : `http://localhost:8001/api/employees/3`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": {
        "id": 3,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "role": "employee",
        "sites": [],
        "pointages": [],
        "created_at": "2024-01-01T00:00:00.000000Z"
    }
}
```

#### Test 7: PUT /api/employees/{id} ✅
**Objectif** : Modifier un employé
**Méthode** : PUT
**URL** : `http://localhost:8001/api/employees/3`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Payload** :
```json
{
    "name": "Jane Smith Updated",
    "email": "<EMAIL>"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Employee updated successfully",
        "fr": "Employé modifié avec succès",
        "ar": "تم تحديث الموظف بنجاح"
    },
    "data": {
        "id": 3,
        "name": "Jane Smith Updated",
        "email": "<EMAIL>",
        "role": "employee"
    }
}
```

#### Test 8: DELETE /api/employees/{id} ✅
**Objectif** : Supprimer un employé
**Méthode** : DELETE
**URL** : `http://localhost:8001/api/employees/3`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Employee deleted successfully",
        "fr": "Employé supprimé avec succès",
        "ar": "تم حذف الموظف بنجاح"
    }
}
```

### 🏗️ Phase 3: Tests Gestion Sites (Admin)

#### Test 9: GET /api/sites ✅
**Objectif** : Récupérer liste des sites
**Méthode** : GET
**URL** : `http://localhost:8001/api/sites`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Chantier Centre-ville",
            "latitude": "33.5731",
            "longitude": "-7.5898",
            "users": [],
            "created_at": "2024-01-01T00:00:00.000000Z"
        }
    ]
}
```

#### Test 10: POST /api/sites ✅
**Objectif** : Créer un nouveau site
**Méthode** : POST
**URL** : `http://localhost:8001/api/sites`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Payload** :
```json
{
    "name": "Nouveau Chantier",
    "latitude": 33.5731,
    "longitude": -7.5898
}
```
**Réponse attendue** : 201 Created
```json
{
    "success": true,
    "message": {
        "en": "Site created successfully",
        "fr": "Site créé avec succès",
        "ar": "تم إنشاء الموقع بنجاح"
    },
    "data": {
        "id": 2,
        "name": "Nouveau Chantier",
        "latitude": "33.5731",
        "longitude": "-7.5898",
        "created_at": "2024-01-01T00:00:00.000000Z"
    }
}
```

#### Test 11: POST /api/assign-site ✅
**Objectif** : Assigner un site à des employés
**Méthode** : POST
**URL** : `http://localhost:8001/api/assign-site`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Payload** :
```json
{
    "site_id": 2,
    "user_ids": [2, 3]
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Site assigned successfully",
        "fr": "Site assigné avec succès",
        "ar": "تم تعيين الموقع بنجاح"
    },
    "data": {
        "id": 2,
        "name": "Nouveau Chantier",
        "latitude": "33.5731",
        "longitude": "-7.5898",
        "users": [
            {
                "id": 2,
                "name": "John Doe",
                "email": "<EMAIL>"
            }
        ]
    }
}
```

#### Test 12: GET /api/my-sites ✅
**Objectif** : Récupérer sites assignés à l'employé connecté
**Méthode** : GET
**URL** : `http://localhost:8001/api/my-sites`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {employee_token}"
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": [
        {
            "id": 2,
            "name": "Nouveau Chantier",
            "latitude": "33.5731",
            "longitude": "-7.5898"
        }
    ]
}
```

### ⏰ Phase 4: Tests Pointage

#### Test 13: POST /api/check-location ✅
**Objectif** : Vérifier si l'employé est dans la zone autorisée
**Méthode** : POST
**URL** : `http://localhost:8001/api/check-location`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {employee_token}"
}
```
**Payload** :
```json
{
    "latitude": 33.5731,
    "longitude": -7.5898,
    "site_id": 2
}
```
**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "message": {
        "en": "Location verified successfully",
        "fr": "Position vérifiée avec succès",
        "ar": "تم التحقق من الموقع بنجاح"
    },
    "data": {
        "within_range": true,
        "distance": 15.5,
        "site": {
            "id": 2,
            "name": "Nouveau Chantier"
        }
    }
}
```

#### Test 14: POST /api/save-pointage ✅
**Objectif** : Enregistrer un pointage
**Méthode** : POST
**URL** : `http://localhost:8001/api/save-pointage`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {employee_token}"
}
```
**Payload** :
```json
{
    "site_id": 2,
    "latitude": 33.5731,
    "longitude": -7.5898,
    "type": "debut"
}
```
**Réponse attendue** : 201 Created
```json
{
    "success": true,
    "message": {
        "en": "Pointage saved successfully",
        "fr": "Pointage enregistré avec succès",
        "ar": "تم حفظ التوقيت بنجاح"
    },
    "data": {
        "id": 1,
        "user_id": 2,
        "site_id": 2,
        "debut_pointage": "2024-01-01T08:00:00.000000Z",
        "debut_latitude": "33.5731",
        "debut_longitude": "-7.5898"
    }
}
```

#### Test 15: GET /api/pointages ✅
**Objectif** : Récupérer liste des pointages (Admin)
**Méthode** : GET
**URL** : `http://localhost:8001/api/pointages`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Paramètres optionnels** :
- `?user_id=2` - Filtrer par employé
- `?site_id=2` - Filtrer par site
- `?date_from=2024-01-01` - Date début
- `?date_to=2024-01-31` - Date fin

**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user": {
                "id": 2,
                "name": "John Doe"
            },
            "site": {
                "id": 2,
                "name": "Nouveau Chantier"
            },
            "debut_pointage": "2024-01-01T08:00:00.000000Z",
            "fin_pointage": null,
            "duree": null
        }
    ]
}
```

### 📍 Phase 5: Tests Vérifications

#### Test 16: POST /api/request-verification ✅
**Objectif** : Demander une vérification de localisation (Admin)
**Méthode** : POST
**URL** : `http://localhost:8001/api/request-verification`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Payload** :
```json
{
    "user_id": 2,
    "message": "Vérification de position demandée"
}
```
**Réponse attendue** : 201 Created
```json
{
    "success": true,
    "message": {
        "en": "Verification request sent successfully",
        "fr": "Demande de vérification envoyée avec succès",
        "ar": "تم إرسال طلب التحقق بنجاح"
    },
    "data": {
        "id": 1,
        "user_id": 2,
        "requested_by": 1,
        "message": "Vérification de position demandée",
        "status": "pending"
    }
}
```

#### Test 17: POST /api/verify-location ✅
**Objectif** : Vérifier position (réponse employé)
**Méthode** : POST
**URL** : `http://localhost:8001/api/verify-location`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {employee_token}"
}
```
**Payload** :
```json
{
    "latitude": 33.5731,
    "longitude": -7.5898
}
```
**Réponse attendue** : 201 Created
```json
{
    "success": true,
    "message": {
        "en": "Location verified successfully",
        "fr": "Position vérifiée avec succès",
        "ar": "تم التحقق من الموقع بنجاح"
    },
    "data": {
        "id": 1,
        "user_id": 2,
        "latitude": "33.5731",
        "longitude": "-7.5898",
        "date_heure": "2024-01-01T10:00:00.000000Z"
    }
}
```

#### Test 18: GET /api/verifications ✅
**Objectif** : Récupérer historique des vérifications (Admin)
**Méthode** : GET
**URL** : `http://localhost:8001/api/verifications`
**Headers** :
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {admin_token}"
}
```
**Paramètres optionnels** :
- `?user_id=2` - Filtrer par employé
- `?date_from=2024-01-01` - Date début
- `?date_to=2024-01-31` - Date fin

**Réponse attendue** : 200 OK
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user": {
                "id": 2,
                "name": "John Doe"
            },
            "latitude": "33.5731",
            "longitude": "-7.5898",
            "date_heure": "2024-01-01T10:00:00.000000Z",
            "created_at": "2024-01-01T10:00:00.000000Z"
        }
    ]
}
```
