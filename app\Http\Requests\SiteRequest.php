<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class SiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Site name is required / Nom du site requis / اسم الموقع مطلوب',
            'name.string' => 'Site name must be a string / Le nom du site doit être une chaîne / يجب أن يكون اسم الموقع نصاً',
            'name.max' => 'Site name must not exceed 255 characters / Le nom du site ne doit pas dépasser 255 caractères / يجب ألا يتجاوز اسم الموقع 255 حرفاً',
            'latitude.required' => 'Latitude is required / Latitude requise / خط العرض مطلوب',
            'latitude.numeric' => 'Invalid latitude format / Format de latitude invalide / تنسيق خط العرض غير صحيح',
            'latitude.between' => 'Latitude must be between -90 and 90 / La latitude doit être entre -90 et 90 / يجب أن يكون خط العرض بين -90 و 90',
            'longitude.required' => 'Longitude is required / Longitude requise / خط الطول مطلوب',
            'longitude.numeric' => 'Invalid longitude format / Format de longitude invalide / تنسيق خط الطول غير صحيح',
            'longitude.between' => 'Longitude must be between -180 and 180 / La longitude doit être entre -180 et 180 / يجب أن يكون خط الطول بين -180 و 180',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Validation failed',
                    'fr' => 'Échec de la validation',
                    'ar' => 'فشل التحقق'
                ],
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
