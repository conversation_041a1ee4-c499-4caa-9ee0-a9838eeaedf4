<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class PointageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'site_id' => 'required|exists:sites,id',
            'debut_pointage' => 'required|date',
            'fin_pointage' => 'nullable|date|after:debut_pointage',
            'debut_latitude' => 'required|numeric|between:-90,90',
            'debut_longitude' => 'required|numeric|between:-180,180',
            'fin_latitude' => 'nullable|numeric|between:-90,90',
            'fin_longitude' => 'nullable|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User ID is required / ID utilisateur requis / معرف المستخدم مطلوب',
            'user_id.exists' => 'User not found / Utilisateur non trouvé / المستخدم غير موجود',
            'site_id.required' => 'Site ID is required / ID du site requis / معرف الموقع مطلوب',
            'site_id.exists' => 'Site not found / Site non trouvé / الموقع غير موجود',
            'debut_pointage.required' => 'Start time is required / Heure de début requise / وقت البداية مطلوب',
            'debut_pointage.date' => 'Invalid start time format / Format d\'heure de début invalide / تنسيق وقت البداية غير صحيح',
            'fin_pointage.date' => 'Invalid end time format / Format d\'heure de fin invalide / تنسيق وقت النهاية غير صحيح',
            'fin_pointage.after' => 'End time must be after start time / L\'heure de fin doit être après l\'heure de début / يجب أن يكون وقت النهاية بعد وقت البداية',
            'debut_latitude.required' => 'Start latitude is required / Latitude de début requise / خط العرض للبداية مطلوب',
            'debut_latitude.numeric' => 'Invalid latitude format / Format de latitude invalide / تنسيق خط العرض غير صحيح',
            'debut_latitude.between' => 'Latitude must be between -90 and 90 / La latitude doit être entre -90 et 90 / يجب أن يكون خط العرض بين -90 و 90',
            'debut_longitude.required' => 'Start longitude is required / Longitude de début requise / خط الطول للبداية مطلوب',
            'debut_longitude.numeric' => 'Invalid longitude format / Format de longitude invalide / تنسيق خط الطول غير صحيح',
            'debut_longitude.between' => 'Longitude must be between -180 and 180 / La longitude doit être entre -180 et 180 / يجب أن يكون خط الطول بين -180 و 180',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Validation failed',
                    'fr' => 'Échec de la validation',
                    'ar' => 'فشل التحقق'
                ],
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
