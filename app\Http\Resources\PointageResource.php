<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PointageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'site_id' => $this->site_id,
            'debut_pointage' => $this->debut_pointage?->format('Y-m-d H:i:s'),
            'fin_pointage' => $this->fin_pointage?->format('Y-m-d H:i:s'),
            'duree' => $this->duree,
            'debut_latitude' => (float) $this->debut_latitude,
            'debut_longitude' => (float) $this->debut_longitude,
            'fin_latitude' => $this->fin_latitude ? (float) $this->fin_latitude : null,
            'fin_longitude' => $this->fin_longitude ? (float) $this->fin_longitude : null,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Relations conditionnelles
            'user' => new UserResource($this->whenLoaded('user')),
            'site' => new SiteResource($this->whenLoaded('site')),
        ];
    }
}
