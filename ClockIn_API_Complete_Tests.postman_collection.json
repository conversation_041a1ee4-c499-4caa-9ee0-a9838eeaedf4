{"info": {"_postman_id": "clockin-api-complete-tests", "name": "ClockIn API - Tests Complets DB", "description": "Collection complète pour tester l'API ClockIn avec validation de la connectivité MySQL via phpMyAdmin (localhost:8080)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8001/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "employee_token", "value": "", "type": "string"}, {"key": "test_employee_id", "value": "", "type": "string"}, {"key": "test_site_id", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication & DB Tests", "item": [{"name": "Login Admin - DB Connectivity Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success true\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('token');", "    pm.collectionVariables.set(\"admin_token\", jsonData.data.token);", "});", "", "pm.test(\"User is admin\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.role).to.eql('admin');", "});", "", "pm.test(\"Database connectivity - User data loaded from MySQL\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user).to.have.property('id');", "    pm.expect(jsonData.data.user).to.have.property('name');", "    pm.expect(jsonData.data.user).to.have.property('email');", "    pm.expect(jsonData.data.user.email).to.eql('<EMAIL>');", "});", "", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "<PERSON><PERSON> - DB Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Employee login successful\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.user.role).to.eql('employee');", "    pm.collectionVariables.set(\"employee_token\", jsonData.data.token);", "});", "", "pm.test(\"Employee data loaded from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.email).to.eql('<EMAIL>');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Get Profile - Sanctum Auth Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Profile retrieved successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('id');", "});", "", "pm.test(\"Sanctum authentication working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.role).to.eql('admin');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}}}]}, {"name": "👥 Employee Management - DB CRUD Tests", "item": [{"name": "List Employees - DB Que<PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Employees list retrieved from database\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Database pagination working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('pagination');", "    pm.expect(jsonData.pagination).to.have.property('total');", "});", "", "pm.test(\"Employee relations loaded\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('id');", "        pm.expect(jsonData.data[0]).to.have.property('name');", "        pm.expect(jsonData.data[0]).to.have.property('email');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/employees?per_page=10", "host": ["{{base_url}}"], "path": ["employees"], "query": [{"key": "per_page", "value": "10"}]}}}, {"name": "Create Employee - DB Insert Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Employee created in database\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.collectionVariables.set(\"test_employee_id\", jsonData.data.id);", "});", "", "pm.test(\"Password hashed correctly\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.not.have.property('password');", "});", "", "pm.test(\"Database constraints respected\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.email).to.eql('<EMAIL>');", "    pm.expect(jsonData.data.role).to.eql('employee');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Employee DB\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"]}}}]}, {"name": "🏗️ Site Management - DB Relations Tests", "item": [{"name": "List Sites - DB Query Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Sites retrieved from database\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"GPS coordinates properly stored\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('latitude');", "        pm.expect(jsonData.data[0]).to.have.property('longitude');", "        pm.expect(jsonData.data[0].latitude).to.be.a('number');", "        pm.expect(jsonData.data[0].longitude).to.be.a('number');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/sites", "host": ["{{base_url}}"], "path": ["sites"]}}}, {"name": "Create Site - DB Insert Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Site created in database\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.collectionVariables.set(\"test_site_id\", jsonData.data.id);", "});", "", "pm.test(\"GPS coordinates stored correctly\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.latitude).to.eql(33.5731);", "    pm.expect(jsonData.data.longitude).to.eql(-7.5898);", "});", "", "pm.test(\"Site name stored correctly\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.name).to.eql('Test Site DB');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Site DB\",\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/sites", "host": ["{{base_url}}"], "path": ["sites"]}}}, {"name": "Assign Site - DB Relation Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Site assignment created in database\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"Assignment table relationship working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.have.property('fr');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": {{test_site_id}},\n    \"user_ids\": [{{test_employee_id}}]\n}"}, "url": {"raw": "{{base_url}}/assign-site", "host": ["{{base_url}}"], "path": ["assign-site"]}}}, {"name": "My Sites - DB Relation Query Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"User sites retrieved via relations\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Assignment relationship working\", function () {", "    var jsonData = pm.response.json();", "    // Should show sites assigned to the authenticated user", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('id');", "        pm.expect(jsonData.data[0]).to.have.property('name');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/my-sites", "host": ["{{base_url}}"], "path": ["my-sites"]}}}]}, {"name": "⏰ Pointage System - DB Complex Relations", "item": [{"name": "Check Location - GPS Validation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Location check processed\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"GPS calculation working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('is_within_range');", "    pm.expect(jsonData.data.is_within_range).to.be.a('boolean');", "});", "", "pm.test(\"Site data loaded from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('site');", "    pm.expect(jsonData.data.site).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": {{test_site_id}},\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/check-location", "host": ["{{base_url}}"], "path": ["check-location"]}}}, {"name": "Save Pointage - DB Insert Complex", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Pointage saved to database\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('id');", "});", "", "pm.test(\"Pointage relations loaded\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('user');", "    pm.expect(jsonData.data).to.have.property('site');", "    pm.expect(jsonData.data.user).to.have.property('name');", "    pm.expect(jsonData.data.site).to.have.property('name');", "});", "", "pm.test(\"GPS coordinates stored correctly\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.debut_latitude).to.eql(33.5731);", "    pm.expect(jsonData.data.debut_longitude).to.eql(-7.5898);", "});", "", "pm.test(\"Datetime fields properly formatted\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('debut_pointage');", "    pm.expect(jsonData.data.debut_pointage).to.match(/\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}/);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": {{test_employee_id}},\n    \"site_id\": {{test_site_id}},\n    \"debut_pointage\": \"2024-01-15 08:00:00\",\n    \"fin_pointage\": \"2024-01-15 17:00:00\",\n    \"debut_latitude\": 33.5731,\n    \"debut_longitude\": -7.5898,\n    \"fin_latitude\": 33.5731,\n    \"fin_longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/save-pointage", "host": ["{{base_url}}"], "path": ["save-pointage"]}}}, {"name": "List Pointages - DB Query with Relations", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Pointages retrieved with relations\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Database pagination working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('pagination');", "});", "", "pm.test(\"Pointage relations preloaded\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('user');", "        pm.expect(jsonData.data[0]).to.have.property('site');", "        pm.expect(jsonData.data[0].user).to.have.property('name');", "        pm.expect(jsonData.data[0].site).to.have.property('name');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/pointages?per_page=10", "host": ["{{base_url}}"], "path": ["pointages"], "query": [{"key": "per_page", "value": "10"}]}}}]}, {"name": "📍 Verification System - DB Logging", "item": [{"name": "Request Verification - DB Insert", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Verification request processed\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"Verification logged in database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.have.property('fr');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": {{test_employee_id}}\n}"}, "url": {"raw": "{{base_url}}/request-verification", "host": ["{{base_url}}"], "path": ["request-verification"]}}}, {"name": "Verify Location - DB Insert with GPS", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Location verification saved\", function () {", "    pm.response.to.have.status(201);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('id');", "});", "", "pm.test(\"GPS coordinates stored in verification\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.latitude).to.eql(33.5731);", "    pm.expect(jsonData.data.longitude).to.eql(-7.5898);", "});", "", "pm.test(\"User relation loaded\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('user');", "    pm.expect(jsonData.data.user).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": {{test_employee_id}},\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898,\n    \"date_heure\": \"2024-01-15 14:35:00\"\n}"}, "url": {"raw": "{{base_url}}/verify-location", "host": ["{{base_url}}"], "path": ["verify-location"]}}}, {"name": "List Verifications - DB Query", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Verifications retrieved from database\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Verification relations loaded\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('user');", "        pm.expect(jsonData.data[0].user).to.have.property('name');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/verifications", "host": ["{{base_url}}"], "path": ["verifications"]}}}]}, {"name": "🚨 Error Handling & DB Constraints", "item": [{"name": "<PERSON><PERSON><PERSON> - DB Security Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Invalid credentials rejected\", function () {", "    pm.response.to.have.status(401);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "});", "", "pm.test(\"Security message returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.have.property('fr');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Duplicate Email - DB Constraint Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Duplicate email rejected\", function () {", "    pm.response.to.have.status(422);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "});", "", "pm.test(\"Validation error returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('errors');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Duplicate Test\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"]}}}, {"name": "Unauthorized Access - Middleware Test", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Unauthorized access blocked\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Authentication required message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"]}}}]}]}