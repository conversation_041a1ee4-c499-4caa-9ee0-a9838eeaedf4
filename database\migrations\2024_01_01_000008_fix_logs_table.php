<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('logs', function (Blueprint $table) {
            // Ajouter les colonnes manquantes
            if (!Schema::hasColumn('logs', 'status')) {
                $table->string('status')->after('action'); // 'success', 'failed'
            }
            if (!Schema::hasColumn('logs', 'ip_address')) {
                $table->string('ip_address')->nullable()->after('details');
            }
            if (!Schema::hasColumn('logs', 'user_agent')) {
                $table->text('user_agent')->nullable()->after('ip_address');
            }
            if (!Schema::hasColumn('logs', 'updated_at')) {
                $table->timestamp('updated_at')->nullable()->after('created_at');
            }
            
            // Modifier user_id pour permettre NULL
            $table->foreignId('user_id')->nullable()->change();
            
            // Ajouter les index manquants
            if (!Schema::hasIndex('logs', ['status'])) {
                $table->index('status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('logs', function (Blueprint $table) {
            $table->dropColumn(['status', 'ip_address', 'user_agent', 'updated_at']);
        });
    }
};
