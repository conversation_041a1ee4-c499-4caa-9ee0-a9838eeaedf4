#!/bin/bash

echo "========================================"
echo "     Installation ClockIn API"
echo "========================================"
echo

echo "1. Installation des dépendances Composer..."
composer install
if [ $? -ne 0 ]; then
    echo "Erreur lors de l'installation des dépendances"
    exit 1
fi

echo
echo "2. Copie du fichier d'environnement..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Fichier .env créé. Veuillez configurer votre base de données."
else
    echo "Fichier .env existe déjà."
fi

echo
echo "3. Génération de la clé d'application..."
php artisan key:generate
if [ $? -ne 0 ]; then
    echo "Erreur lors de la génération de la clé"
    exit 1
fi

echo
echo "4. Vérification de la configuration de la base de données..."
echo "Assurez-vous que votre base de données 'clockin_db' existe dans MySQL."
echo "Configuration actuelle dans .env :"
grep "DB_" .env

echo
read -p "Continuer avec les migrations ? (o/n): " continue
if [ "$continue" != "o" ] && [ "$continue" != "O" ]; then
    echo "Installation interrompue."
    exit 0
fi

echo
echo "5. Exécution des migrations..."
php artisan migrate
if [ $? -ne 0 ]; then
    echo "Erreur lors des migrations"
    echo "Vérifiez votre configuration de base de données"
    exit 1
fi

echo
echo "6. Exécution des seeders..."
php artisan db:seed
if [ $? -ne 0 ]; then
    echo "Erreur lors du seeding"
    exit 1
fi

echo
echo "7. Publication de la configuration Sanctum..."
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

echo
echo "========================================"
echo "     Installation terminée avec succès !"
echo "========================================"
echo
echo "Comptes de test créés :"
echo "- Admin: <EMAIL> / password123"
echo "- Employés: <EMAIL>, <EMAIL>, <EMAIL> / password123"
echo
echo "Pour démarrer le serveur :"
echo "php artisan serve"
echo
echo "L'API sera accessible sur : http://localhost:8000/api"
echo "Documentation : Voir API_DOCUMENTATION.md"
echo
