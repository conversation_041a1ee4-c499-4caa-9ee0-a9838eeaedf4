@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo     TESTS COMPLETS CLOCKIN API ^& DATABASE
echo ===============================================
echo.

:: Vérifier si PHP est disponible
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP n'est pas disponible dans le PATH
    echo Assurez-vous que WampServer est installé et configuré
    pause
    exit /b 1
)

echo ✅ PHP disponible
echo.

:: Vérifier les fichiers essentiels
if not exist .env (
    echo ❌ Fichier .env manquant
    if exist .env.example (
        echo 📋 Copie de .env.example vers .env...
        copy .env.example .env >nul
        echo ✅ Fichier .env créé
    ) else (
        echo Créez le fichier .env à partir de .env.example
        pause
        exit /b 1
    )
)

if not exist artisan (
    echo ❌ Fichier artisan manquant - Êtes-vous dans le bon répertoire ?
    pause
    exit /b 1
)

echo ✅ Fichiers essentiels trouvés
echo.

:: Générer la clé d'application si nécessaire
findstr /C:"APP_KEY=" .env | findstr /C:"APP_KEY=base64:" >nul
if errorlevel 1 (
    echo 🔑 Génération de la clé d'application...
    php artisan key:generate --quiet
    if errorlevel 1 (
        echo ❌ Erreur lors de la génération de la clé
        pause
        exit /b 1
    )
    echo ✅ Clé d'application générée
)

echo.
echo 📊 Configuration actuelle:
findstr "DB_\|APP_URL" .env
echo.

:: Vérifier si WampServer/phpMyAdmin est accessible
echo 🌐 Vérification des services...
curl -s --connect-timeout 3 http://localhost:8080/phpmyadmin >nul 2>&1
if errorlevel 1 (
    echo ❌ phpMyAdmin non accessible - WampServer démarré ?
    echo 💡 Démarrez WampServer et assurez-vous que MySQL est actif
    set /p continue="Continuer malgré tout ? (o/n): "
    if /i "!continue!" neq "o" (
        pause
        exit /b 1
    )
) else (
    echo ✅ phpMyAdmin accessible
)

:: Vérifier si le serveur Laravel est démarré
curl -s --connect-timeout 3 http://localhost:8001 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Serveur Laravel non accessible - démarrage...
    start /B php artisan serve --port=8001
    timeout /t 3 /nobreak >nul
    
    curl -s --connect-timeout 3 http://localhost:8001 >nul 2>&1
    if errorlevel 1 (
        echo ❌ Impossible de démarrer le serveur Laravel
        echo 💡 Démarrez manuellement: php artisan serve --port=8001
    ) else (
        echo ✅ Serveur Laravel démarré
    )
) else (
    echo ✅ Serveur Laravel accessible
)

echo.
echo ===============================================
echo EXÉCUTION DES TESTS COMPLETS
echo ===============================================
echo.

:: Exécuter le script de test principal
echo 🚀 Lancement des tests complets...
echo.

php run_all_tests.php

echo.
echo ===============================================
echo TESTS TERMINÉS
echo ===============================================
echo.

echo 💡 LIENS UTILES:
echo - phpMyAdmin: http://localhost:8080/phpmyadmin
echo - API ClockIn: http://localhost:8001/api
echo - Base de données: clockin_db
echo.

echo 📋 FICHIERS DE TEST CRÉÉS:
echo - run_all_tests.php (test principal)
echo - test_all_apis.php (test base de données)
echo - test_api_endpoints.php (test endpoints)
echo - TESTS_API_GUIDE.md (documentation)
echo.

pause
