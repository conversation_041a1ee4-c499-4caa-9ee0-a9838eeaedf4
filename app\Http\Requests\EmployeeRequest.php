<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('id');
        
        return [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($userId)
            ],
            'password' => $this->isMethod('POST') ? 'required|string|min:6' : 'nullable|string|min:6',
            'role' => 'required|in:admin,employee',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required / Nom requis / الاسم مطلوب',
            'name.string' => 'Name must be a string / Le nom doit être une chaîne / يجب أن يكون الاسم نصاً',
            'name.max' => 'Name must not exceed 255 characters / Le nom ne doit pas dépasser 255 caractères / يجب ألا يتجاوز الاسم 255 حرفاً',
            'email.required' => 'Email is required / Email requis / البريد الإلكتروني مطلوب',
            'email.email' => 'Invalid email format / Format email invalide / تنسيق البريد الإلكتروني غير صحيح',
            'email.unique' => 'Email already exists / Email déjà existant / البريد الإلكتروني موجود بالفعل',
            'password.required' => 'Password is required / Mot de passe requis / كلمة المرور مطلوبة',
            'password.string' => 'Password must be a string / Le mot de passe doit être une chaîne / يجب أن تكون كلمة المرور نصاً',
            'password.min' => 'Password must be at least 6 characters / Le mot de passe doit contenir au moins 6 caractères / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل',
            'role.required' => 'Role is required / Rôle requis / الدور مطلوب',
            'role.in' => 'Role must be admin or employee / Le rôle doit être admin ou employee / يجب أن يكون الدور admin أو employee',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Validation failed',
                    'fr' => 'Échec de la validation',
                    'ar' => 'فشل التحقق'
                ],
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
