{"info": {"_postman_id": "clockin-api-tests-2024", "name": "ClockIn API - Tests Complets", "description": "Collection complète pour tester tous les endpoints de l'API ClockIn avec validation de la base de données MySQL", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8001", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "employee_token", "value": "", "type": "string"}, {"key": "test_user_id", "value": "", "type": "string"}, {"key": "test_site_id", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication Tests", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Response has success=true\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"✅ Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.token).to.exist;", "    pm.collectionVariables.set(\"admin_token\", jsonData.data.token);", "});", "", "pm.test(\"✅ User is admin\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.role).to.eql(\"admin\");", "});", "", "pm.test(\"✅ Database connection working\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.id).to.exist;", "    pm.expect(jsonData.data.user.email).to.eql(\"<EMAIL>\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}}}, {"name": "<PERSON><PERSON> Employee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.token).to.exist;", "    pm.collectionVariables.set(\"employee_token\", jsonData.data.token);", "});", "", "pm.test(\"✅ User is employee\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.role).to.eql(\"employee\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}}}, {"name": "Get User Profile (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ User data retrieved from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.name).to.exist;", "    pm.expect(jsonData.data.email).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/me", "host": ["{{base_url}}"], "path": ["api", "me"]}}}]}, {"name": "👥 Employee Management (Admin Only)", "item": [{"name": "Get All Employees", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Database query successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"✅ Pagination data exists\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.pagination).to.exist;", "    pm.expect(jsonData.pagination.total).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}}, {"name": "Create New Employee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"✅ Employee created in database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.id).to.exist;", "    pm.collectionVariables.set(\"test_user_id\", jsonData.data.id);", "});", "", "pm.test(\"✅ Employee data correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.name).to.eql(\"Test Employee\");", "    pm.expect(jsonData.data.email).to.eql(\"<EMAIL>\");", "    pm.expect(jsonData.data.role).to.eql(\"employee\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Employee\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}}, {"name": "Get Employee by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Employee data retrieved from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.name).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/employees/{{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "employees", "{{test_user_id}}"]}}}]}, {"name": "🏢 Site Management (Admin Only)", "item": [{"name": "Get All Sites", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Sites data from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "    if (jsonData.data.length > 0) {", "        pm.collectionVariables.set(\"test_site_id\", jsonData.data[0].id);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/sites", "host": ["{{base_url}}"], "path": ["api", "sites"]}}}]}, {"name": "⏰ Pointage Tests", "item": [{"name": "Check Location", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Location check response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.is_within_range).to.be.a('boolean');", "});", "", "pm.test(\"✅ Database interaction successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.site).to.exist;", "    pm.expect(jsonData.data.site.id).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": \"{{test_site_id}}\",\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/api/check-location", "host": ["{{base_url}}"], "path": ["api", "check-location"]}}}, {"name": "Save Pointage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"✅ Pointage saved to database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.user_id).to.exist;", "    pm.expect(jsonData.data.site_id).to.exist;", "});", "", "pm.test(\"✅ Pointage data correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.debut_latitude).to.exist;", "    pm.expect(jsonData.data.debut_longitude).to.exist;", "    pm.expect(jsonData.data.debut_pointage).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"{{test_user_id}}\",\n    \"site_id\": \"{{test_site_id}}\",\n    \"debut_pointage\": \"2024-01-15 08:00:00\",\n    \"debut_latitude\": 33.5731,\n    \"debut_longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/api/save-pointage", "host": ["{{base_url}}"], "path": ["api", "save-pointage"]}}}, {"name": "Get All Pointages (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Pointages retrieved from database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"✅ Pagination and relations loaded\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.pagination).to.exist;", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0].user).to.exist;", "        pm.expect(jsonData.data[0].site).to.exist;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/pointages", "host": ["{{base_url}}"], "path": ["api", "pointages"]}}}]}, {"name": "📍 Verification Tests", "item": [{"name": "Verify Location", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"✅ Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"✅ Verification saved to database\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.id).to.exist;", "});", "", "pm.test(\"✅ Location data correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.latitude).to.exist;", "    pm.expect(jsonData.data.longitude).to.exist;", "    pm.expect(jsonData.data.date_heure).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 5,\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898,\n    \"date_heure\": \"2024-01-15 10:30:00\"\n}"}, "url": {"raw": "{{base_url}}/api/verify-location", "host": ["{{base_url}}"], "path": ["api", "verify-location"]}}}]}]}