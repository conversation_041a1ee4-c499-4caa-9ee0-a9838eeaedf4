<?php

/**
 * Script pour tester la connectivité et l'intégrité de la base de données MySQL
 * Sans modifier la structure existante
 */

require_once 'vendor/autoload.php';

echo "🗄️  TEST DE CONNECTIVITÉ BASE DE DONNÉES MYSQL\n";
echo "===============================================\n\n";

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Verification;
use App\Models\Assignment;
use App\Models\Log;

$testResults = [];

/**
 * Enregistrer un résultat de test
 */
function recordTest($testName, $success, $details = '') {
    global $testResults;
    
    $status = $success ? '✅' : '❌';
    echo "{$status} {$testName}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    
    $testResults[] = [
        'name' => $testName,
        'success' => $success,
        'details' => $details
    ];
}

/**
 * Test de connexion MySQL de base
 */
function testBasicConnection() {
    echo "1. TEST DE CONNEXION MYSQL\n";
    echo "===========================\n";
    
    try {
        // Test de connexion simple
        $pdo = DB::connection()->getPdo();
        recordTest('Connexion MySQL', true, 'PDO connecté');
        
        // Vérifier la base de données
        $dbName = DB::connection()->getDatabaseName();
        recordTest('Base de données', true, "Connecté à: {$dbName}");
        
        // Test de requête simple
        $result = DB::select('SELECT 1 as test');
        if ($result && $result[0]->test == 1) {
            recordTest('Requête SQL de base', true, 'SELECT fonctionne');
        } else {
            recordTest('Requête SQL de base', false, 'Problème avec SELECT');
        }
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Connexion MySQL', false, $e->getMessage());
        return false;
    }
}

/**
 * Test de l'existence des tables
 */
function testTablesExistence() {
    echo "\n2. VÉRIFICATION DES TABLES\n";
    echo "===========================\n";
    
    try {
        // Récupérer la liste des tables
        $tables = DB::select("SHOW TABLES");
        $tableNames = array_map(function($table) {
            $tableName = array_values((array)$table)[0];
            return $tableName;
        }, $tables);
        
        // Tables requises
        $requiredTables = [
            'users', 'sites', 'pointages', 'verifications', 
            'assignments', 'logs', 'migrations', 'personal_access_tokens'
        ];
        
        foreach ($requiredTables as $table) {
            if (in_array($table, $tableNames)) {
                recordTest("Table '{$table}'", true, 'Existe');
            } else {
                recordTest("Table '{$table}'", false, 'Manquante');
            }
        }
        
        recordTest('Total tables trouvées', true, count($tableNames) . ' tables');
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Vérification tables', false, $e->getMessage());
        return false;
    }
}

/**
 * Test des contraintes et relations
 */
function testConstraints() {
    echo "\n3. VÉRIFICATION DES CONTRAINTES\n";
    echo "================================\n";
    
    try {
        // Vérifier les contraintes de clés étrangères
        $constraints = DB::select("
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        recordTest('Contraintes de clés étrangères', true, count($constraints) . ' contraintes trouvées');
        
        // Vérifier quelques contraintes importantes
        $importantConstraints = [
            'pointages' => ['user_id', 'site_id'],
            'assignments' => ['user_id', 'site_id'],
            'verifications' => ['user_id'],
            'logs' => ['user_id']
        ];
        
        foreach ($importantConstraints as $table => $columns) {
            foreach ($columns as $column) {
                $found = false;
                foreach ($constraints as $constraint) {
                    if ($constraint->TABLE_NAME === $table && $constraint->COLUMN_NAME === $column) {
                        $found = true;
                        break;
                    }
                }
                
                if ($found) {
                    recordTest("Contrainte {$table}.{$column}", true, 'Clé étrangère configurée');
                } else {
                    recordTest("Contrainte {$table}.{$column}", false, 'Clé étrangère manquante');
                }
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Vérification contraintes', false, $e->getMessage());
        return false;
    }
}

/**
 * Test des modèles Eloquent
 */
function testEloquentModels() {
    echo "\n4. TEST DES MODÈLES ELOQUENT\n";
    echo "=============================\n";
    
    try {
        // Test du modèle User
        $userCount = User::count();
        recordTest('Modèle User', true, "{$userCount} utilisateurs");
        
        // Test du modèle Site
        $siteCount = Site::count();
        recordTest('Modèle Site', true, "{$siteCount} sites");
        
        // Test du modèle Pointage
        $pointageCount = Pointage::count();
        recordTest('Modèle Pointage', true, "{$pointageCount} pointages");
        
        // Test du modèle Verification
        $verificationCount = Verification::count();
        recordTest('Modèle Verification', true, "{$verificationCount} vérifications");
        
        // Test du modèle Assignment
        $assignmentCount = Assignment::count();
        recordTest('Modèle Assignment', true, "{$assignmentCount} assignations");
        
        // Test du modèle Log
        $logCount = Log::count();
        recordTest('Modèle Log', true, "{$logCount} logs");
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Test modèles Eloquent', false, $e->getMessage());
        return false;
    }
}

/**
 * Test des relations entre modèles
 */
function testModelRelations() {
    echo "\n5. TEST DES RELATIONS ELOQUENT\n";
    echo "===============================\n";
    
    try {
        // Test des relations si des données existent
        $users = User::limit(1)->get();
        $sites = Site::limit(1)->get();
        
        if ($users->count() > 0) {
            $user = $users->first();
            
            // Test relation User -> Sites
            try {
                $userSites = $user->sites;
                recordTest('Relation User->Sites', true, 'Fonctionne');
            } catch (Exception $e) {
                recordTest('Relation User->Sites', false, $e->getMessage());
            }
            
            // Test relation User -> Pointages
            try {
                $userPointages = $user->pointages;
                recordTest('Relation User->Pointages', true, 'Fonctionne');
            } catch (Exception $e) {
                recordTest('Relation User->Pointages', false, $e->getMessage());
            }
            
            // Test relation User -> Verifications
            try {
                $userVerifications = $user->verifications;
                recordTest('Relation User->Verifications', true, 'Fonctionne');
            } catch (Exception $e) {
                recordTest('Relation User->Verifications', false, $e->getMessage());
            }
        } else {
            recordTest('Relations User', false, 'Aucun utilisateur pour tester');
        }
        
        if ($sites->count() > 0) {
            $site = $sites->first();
            
            // Test relation Site -> Users
            try {
                $siteUsers = $site->users;
                recordTest('Relation Site->Users', true, 'Fonctionne');
            } catch (Exception $e) {
                recordTest('Relation Site->Users', false, $e->getMessage());
            }
            
            // Test relation Site -> Pointages
            try {
                $sitePointages = $site->pointages;
                recordTest('Relation Site->Pointages', true, 'Fonctionne');
            } catch (Exception $e) {
                recordTest('Relation Site->Pointages', false, $e->getMessage());
            }
        } else {
            recordTest('Relations Site', false, 'Aucun site pour tester');
        }
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Test relations', false, $e->getMessage());
        return false;
    }
}

/**
 * Test des opérations CRUD de base
 */
function testBasicCRUD() {
    echo "\n6. TEST OPÉRATIONS CRUD\n";
    echo "========================\n";
    
    try {
        // Test de lecture (READ)
        $users = User::take(5)->get();
        recordTest('Opération READ', true, 'SELECT fonctionne');
        
        // Test de comptage
        $totalUsers = User::count();
        recordTest('Opération COUNT', true, "{$totalUsers} enregistrements");
        
        // Test de requête avec WHERE
        $admins = User::where('role', 'admin')->count();
        recordTest('Opération WHERE', true, "{$admins} admins trouvés");
        
        // Test de requête avec JOIN (via relations)
        $usersWithSites = User::with('sites')->take(1)->get();
        recordTest('Opération JOIN (relations)', true, 'Relations chargées');
        
        return true;
        
    } catch (Exception $e) {
        recordTest('Test CRUD', false, $e->getMessage());
        return false;
    }
}

// Exécution des tests
$allTestsPassed = true;

$allTestsPassed &= testBasicConnection();
$allTestsPassed &= testTablesExistence();
$allTestsPassed &= testConstraints();
$allTestsPassed &= testEloquentModels();
$allTestsPassed &= testModelRelations();
$allTestsPassed &= testBasicCRUD();

// Résumé final
echo "\n📊 RÉSUMÉ DES TESTS DE BASE DE DONNÉES\n";
echo "======================================\n";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults, function($test) { return $test['success']; }));
$failedTests = $totalTests - $passedTests;

echo "Total des tests: {$totalTests}\n";
echo "✅ Réussis: {$passedTests}\n";
echo "❌ Échoués: {$failedTests}\n";

if ($failedTests === 0) {
    echo "\n🎉 TOUS LES TESTS DE BASE DE DONNÉES SONT RÉUSSIS !\n";
    echo "✅ La base de données MySQL clockin_db est parfaitement configurée\n";
    echo "✅ Toutes les tables et contraintes sont en place\n";
    echo "✅ Les modèles Eloquent fonctionnent correctement\n";
    echo "✅ Toutes les relations sont opérationnelles\n";
    echo "✅ Les opérations CRUD de base fonctionnent\n";
} else {
    echo "\n⚠️ CERTAINS TESTS DE BASE DE DONNÉES ONT ÉCHOUÉ\n";
    echo "Vérifiez les erreurs ci-dessus avant de continuer\n";
}

echo "\n💡 ACCÈS À LA BASE DE DONNÉES:\n";
echo "- phpMyAdmin: http://localhost:8080/phpmyadmin\n";
echo "- Base de données: clockin_db\n";
echo "- Serveur: localhost:3306\n";
echo "- Utilisateur: root (sans mot de passe)\n";
