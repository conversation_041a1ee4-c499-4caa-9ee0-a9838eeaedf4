# 🧪 Guide Complet des Tests API ClockIn

## 📋 Vue d'ensemble

Ce guide vous explique comment tester tous les endpoints API de ClockIn et vérifier la connectivité avec la base de données MySQL sans modifier la structure existante.

## 🚀 Démarrage Rapide

### Option 1: Script Automatique (Recommandé)
```bash
# Windows
test_all_apis.bat

# Linux/Mac
php run_complete_tests.php
```

### Option 2: Tests Manuels
```bash
# 1. Test de connectivité DB
php test_database_connectivity.php

# 2. Test des APIs
php test_api_complete.php

# 3. Démarrer le serveur
php artisan serve --port=8001
```

## 🗄️ Tests de Base de Données

### Vérifications Effectuées

1. **Connectivité MySQL**
   - Connexion PDO
   - Accès à la base `clockin_db`
   - Requêtes SQL de base

2. **Structure des Tables**
   - `users` - Utilisateurs (admin/employés)
   - `sites` - Chantiers avec coordonnées GPS
   - `pointages` - Enregistrements de pointage
   - `verifications` - Vérifications de localisation
   - `assignments` - Attribution employés-chantiers
   - `logs` - Logs de traçabilité
   - `personal_access_tokens` - Tokens Sanctum

3. **Contraintes et Relations**
   - Clés étrangères
   - Index de performance
   - Contraintes d'intégrité

4. **Modèles Eloquent**
   - Fonctionnement des modèles
   - Relations entre entités
   - Opérations CRUD

## 🔗 Tests des Endpoints API

### 🔐 Authentification

| Endpoint | Méthode | Description | Test |
|----------|---------|-------------|------|
| `/api/login` | POST | Connexion utilisateur | ✅ Token généré |
| `/api/logout` | POST | Déconnexion | ✅ Token révoqué |
| `/api/me` | GET | Profil utilisateur | ✅ Données récupérées |

### 👥 Gestion Employés (Admin)

| Endpoint | Méthode | Description | Test |
|----------|---------|-------------|------|
| `/api/employees` | GET | Liste employés | ✅ Pagination |
| `/api/employees` | POST | Créer employé | ✅ Insertion DB |
| `/api/employees/{id}` | GET | Détails employé | ✅ Relations |
| `/api/employees/{id}` | PUT | Modifier employé | ✅ Mise à jour |
| `/api/employees/{id}` | DELETE | Supprimer employé | ✅ Suppression |

### 🏢 Gestion Sites (Admin)

| Endpoint | Méthode | Description | Test |
|----------|---------|-------------|------|
| `/api/sites` | GET | Liste sites | ✅ Données GPS |
| `/api/sites` | POST | Créer site | ✅ Validation |
| `/api/sites/{id}` | GET | Détails site | ✅ Relations |
| `/api/sites/{id}` | PUT | Modifier site | ✅ Coordonnées |
| `/api/sites/{id}` | DELETE | Supprimer site | ✅ Contraintes |

### ⏰ Pointage

| Endpoint | Méthode | Description | Test |
|----------|---------|-------------|------|
| `/api/check-location` | POST | Vérifier position | ✅ Calcul distance |
| `/api/save-pointage` | POST | Enregistrer pointage | ✅ Données GPS |
| `/api/pointages` | GET | Liste pointages | ✅ Filtres |

### 📍 Vérification

| Endpoint | Méthode | Description | Test |
|----------|---------|-------------|------|
| `/api/verify-location` | POST | Vérifier localisation | ✅ Enregistrement |
| `/api/my-sites` | GET | Sites utilisateur | ✅ Assignations |

## 📊 Collection Postman

### Import de la Collection

1. Ouvrez Postman
2. Cliquez sur **Import**
3. Sélectionnez `ClockIn_API_Tests.postman_collection.json`
4. La collection sera importée avec toutes les variables

### Variables de Collection

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `base_url` | URL de base API | `http://localhost:8001` |
| `admin_token` | Token admin | Auto-généré |
| `employee_token` | Token employé | Auto-généré |
| `test_user_id` | ID utilisateur test | Auto-généré |
| `test_site_id` | ID site test | Auto-généré |

### Exécution des Tests

1. **Tests Individuels**: Cliquez sur chaque requête
2. **Tests en Lot**: Cliquez sur "Run Collection"
3. **Tests Automatisés**: Utilisez Newman CLI

```bash
# Installer Newman
npm install -g newman

# Exécuter la collection
newman run ClockIn_API_Tests.postman_collection.json
```

## 🔍 Validation des Tests

### Tests de Connectivité DB

```bash
✅ Connexion MySQL - PDO connecté
✅ Base de données - Connecté à: clockin_db
✅ Requête SQL de base - SELECT fonctionne
✅ Table 'users' - Existe
✅ Table 'sites' - Existe
✅ Table 'pointages' - Existe
✅ Contraintes de clés étrangères - 8 contraintes trouvées
✅ Modèle User - 3 utilisateurs
✅ Relation User->Sites - Fonctionne
```

### Tests API

```bash
✅ Status code is 200
✅ Response has success=true
✅ Response has token
✅ User is admin
✅ Database connection working
✅ Employee created in database
✅ Pointage saved to database
✅ Location check response
```

## 🛠️ Résolution des Problèmes

### Erreurs Communes

1. **Serveur non accessible**
   ```bash
   # Démarrer le serveur
   php artisan serve --port=8001
   ```

2. **Base de données non connectée**
   ```bash
   # Vérifier la configuration
   php artisan config:cache
   php artisan migrate:status
   ```

3. **Tokens invalides**
   ```bash
   # Nettoyer les tokens
   php artisan sanctum:prune-expired
   ```

4. **Permissions de fichiers**
   ```bash
   # Windows (dans le dossier du projet)
   icacls storage /grant Everyone:F /T
   icacls bootstrap/cache /grant Everyone:F /T
   ```

### Vérifications Manuelles

1. **phpMyAdmin**: http://localhost:8080/phpmyadmin
   - Base de données: `clockin_db`
   - Vérifiez les tables et données

2. **API Health Check**: http://localhost:8001/up
   - Doit retourner un statut 200

3. **Logs Laravel**: `storage/logs/laravel.log`
   - Vérifiez les erreurs récentes

## 📈 Métriques de Performance

### Tests de Charge

```bash
# Test avec Apache Bench
ab -n 100 -c 10 http://localhost:8001/api/login

# Test avec curl
for i in {1..10}; do
  curl -X POST http://localhost:8001/api/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}'
done
```

### Monitoring DB

```sql
-- Vérifier les connexions actives
SHOW PROCESSLIST;

-- Vérifier les performances
SHOW STATUS LIKE 'Slow_queries';

-- Vérifier l'utilisation des index
EXPLAIN SELECT * FROM pointages WHERE user_id = 1;
```

## 🎯 Checklist de Validation

- [ ] ✅ WampServer démarré
- [ ] ✅ Base de données `clockin_db` accessible
- [ ] ✅ Serveur Laravel sur port 8001
- [ ] ✅ Toutes les tables existent
- [ ] ✅ Contraintes de clés étrangères
- [ ] ✅ Modèles Eloquent fonctionnels
- [ ] ✅ Relations entre entités
- [ ] ✅ Authentification Sanctum
- [ ] ✅ Endpoints CRUD complets
- [ ] ✅ Validation des données
- [ ] ✅ Gestion des erreurs
- [ ] ✅ Logs de traçabilité

## 📞 Support

En cas de problème:

1. Vérifiez les logs: `storage/logs/laravel.log`
2. Testez la DB: `php test_database_connectivity.php`
3. Vérifiez la configuration: `php artisan config:show`
4. Consultez phpMyAdmin: http://localhost:8080/phpmyadmin

---

**Note**: Ces tests garantissent que l'API ClockIn fonctionne correctement avec la base de données MySQL sans modifier la structure existante.
