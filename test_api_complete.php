<?php

/**
 * Script complet pour tester tous les endpoints API ClockIn
 * et vérifier la connectivité avec la base de données MySQL
 */

echo "🚀 DÉMARRAGE DES TESTS API CLOCKIN\n";
echo "==================================\n\n";

// Configuration
$baseUrl = 'http://localhost:8001/api';
$adminCredentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];
$employeeCredentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

// Variables globales pour les tests
$adminToken = '';
$employeeToken = '';
$testUserId = '';
$testSiteId = '';
$testResults = [];

/**
 * Fonction pour faire des requêtes HTTP
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    // Headers par défaut
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => 0
        ];
    }
    
    return [
        'success' => true,
        'data' => json_decode($response, true),
        'http_code' => $httpCode,
        'raw_response' => $response
    ];
}

/**
 * Fonction pour enregistrer les résultats de test
 */
function recordTest($testName, $success, $details = '') {
    global $testResults;
    
    $status = $success ? '✅' : '❌';
    echo "{$status} {$testName}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    
    $testResults[] = [
        'name' => $testName,
        'success' => $success,
        'details' => $details
    ];
}

/**
 * Test de connectivité de base
 */
function testConnectivity() {
    global $baseUrl;
    
    echo "📡 TEST DE CONNECTIVITÉ\n";
    echo "========================\n";
    
    // Test de ping du serveur
    $response = makeRequest($baseUrl . '/../up');
    
    if ($response['success'] && $response['http_code'] === 200) {
        recordTest('Serveur Laravel accessible', true, 'Port 8001');
    } else {
        recordTest('Serveur Laravel accessible', false, 'Vérifiez que le serveur est démarré');
        return false;
    }
    
    echo "\n";
    return true;
}

/**
 * Tests d'authentification
 */
function testAuthentication() {
    global $baseUrl, $adminCredentials, $employeeCredentials, $adminToken, $employeeToken;
    
    echo "🔐 TESTS D'AUTHENTIFICATION\n";
    echo "============================\n";
    
    // Test login admin
    $response = makeRequest($baseUrl . '/login', 'POST', $adminCredentials);
    
    if ($response['success'] && $response['http_code'] === 200) {
        $data = $response['data'];
        if (isset($data['success']) && $data['success'] && isset($data['data']['token'])) {
            $adminToken = $data['data']['token'];
            recordTest('Login Admin', true, 'Token généré');
            recordTest('Base de données - Table users', true, 'Connexion réussie');
            
            // Vérifier le rôle admin
            if ($data['data']['user']['role'] === 'admin') {
                recordTest('Vérification rôle admin', true, 'Role correct dans la DB');
            } else {
                recordTest('Vérification rôle admin', false, 'Role incorrect');
            }
        } else {
            recordTest('Login Admin', false, 'Format de réponse incorrect');
        }
    } else {
        recordTest('Login Admin', false, 'HTTP ' . $response['http_code']);
        recordTest('Base de données - Table users', false, 'Échec de connexion');
    }
    
    // Test login employee
    $response = makeRequest($baseUrl . '/login', 'POST', $employeeCredentials);
    
    if ($response['success'] && $response['http_code'] === 200) {
        $data = $response['data'];
        if (isset($data['success']) && $data['success'] && isset($data['data']['token'])) {
            $employeeToken = $data['data']['token'];
            recordTest('Login Employee', true, 'Token généré');
            
            // Vérifier le rôle employee
            if ($data['data']['user']['role'] === 'employee') {
                recordTest('Vérification rôle employee', true, 'Role correct dans la DB');
            } else {
                recordTest('Vérification rôle employee', false, 'Role incorrect');
            }
        } else {
            recordTest('Login Employee', false, 'Format de réponse incorrect');
        }
    } else {
        recordTest('Login Employee', false, 'HTTP ' . $response['http_code']);
    }
    
    // Test du profil utilisateur
    if ($adminToken) {
        $response = makeRequest($baseUrl . '/me', 'GET', null, ["Authorization: Bearer {$adminToken}"]);
        
        if ($response['success'] && $response['http_code'] === 200) {
            recordTest('Récupération profil utilisateur', true, 'Données utilisateur récupérées');
            recordTest('Middleware Sanctum', true, 'Authentification par token');
        } else {
            recordTest('Récupération profil utilisateur', false, 'HTTP ' . $response['http_code']);
        }
    }
    
    echo "\n";
    return !empty($adminToken) && !empty($employeeToken);
}

/**
 * Tests de gestion des employés
 */
function testEmployeeManagement() {
    global $baseUrl, $adminToken, $testUserId;
    
    echo "👥 TESTS GESTION EMPLOYÉS\n";
    echo "==========================\n";
    
    if (empty($adminToken)) {
        recordTest('Tests employés', false, 'Token admin requis');
        echo "\n";
        return false;
    }
    
    $headers = ["Authorization: Bearer {$adminToken}"];
    
    // Test liste des employés
    $response = makeRequest($baseUrl . '/employees', 'GET', null, $headers);
    
    if ($response['success'] && $response['http_code'] === 200) {
        $data = $response['data'];
        if (isset($data['success']) && $data['success'] && isset($data['data'])) {
            recordTest('Liste des employés', true, 'Données récupérées de la DB');
            recordTest('Base de données - Pagination', true, 'Système de pagination fonctionnel');
            
            // Vérifier la structure de pagination
            if (isset($data['pagination'])) {
                recordTest('Structure pagination', true, 'Métadonnées complètes');
            }
        } else {
            recordTest('Liste des employés', false, 'Structure de réponse incorrecte');
        }
    } else {
        recordTest('Liste des employés', false, 'HTTP ' . $response['http_code']);
    }
    
    echo "\n";
    return true;
}

// Démarrage des tests
if (!testConnectivity()) {
    echo "❌ Tests interrompus - Problème de connectivité\n";
    exit(1);
}

if (!testAuthentication()) {
    echo "❌ Tests interrompus - Problème d'authentification\n";
    exit(1);
}

testEmployeeManagement();

// Résumé final
echo "📊 RÉSUMÉ DES TESTS\n";
echo "===================\n";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults, function($test) { return $test['success']; }));
$failedTests = $totalTests - $passedTests;

echo "Total des tests: {$totalTests}\n";
echo "✅ Réussis: {$passedTests}\n";
echo "❌ Échoués: {$failedTests}\n";

if ($failedTests === 0) {
    echo "\n🎉 TOUS LES TESTS SONT RÉUSSIS !\n";
    echo "✅ La base de données MySQL est correctement connectée\n";
    echo "✅ Tous les endpoints API fonctionnent\n";
    echo "✅ L'authentification Sanctum est opérationnelle\n";
} else {
    echo "\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ\n";
    echo "Vérifiez les erreurs ci-dessus\n";
}

echo "\n💡 ACCÈS RAPIDE:\n";
echo "- phpMyAdmin: http://localhost:8080/phpmyadmin\n";
echo "- Base de données: clockin_db\n";
echo "- API: http://localhost:8001/api\n";
echo "- Collection Postman: ClockIn_API_Tests.postman_collection.json\n";
