@echo off
echo ========================================
echo     Installation ClockIn API
echo ========================================
echo.

echo 1. Installation des dependances Composer...
composer install
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation des dependances
    pause
    exit /b 1
)

echo.
echo 2. Copie du fichier d'environnement...
if not exist .env (
    copy .env.example .env
    echo Fichier .env cree. Veuillez configurer votre base de donnees.
) else (
    echo Fichier .env existe deja.
)

echo.
echo 3. Generation de la cle d'application...
php artisan key:generate
if %errorlevel% neq 0 (
    echo Erreur lors de la generation de la cle
    pause
    exit /b 1
)

echo.
echo 4. Verification de la configuration de la base de donnees...
echo Assurez-vous que votre base de donnees 'clockin_db' existe dans MySQL.
echo Configuration actuelle dans .env :
findstr "DB_" .env

echo.
set /p continue="Continuer avec les migrations ? (o/n): "
if /i "%continue%" neq "o" (
    echo Installation interrompue.
    pause
    exit /b 0
)

echo.
echo 5. Execution des migrations...
php artisan migrate
if %errorlevel% neq 0 (
    echo Erreur lors des migrations
    echo Verifiez votre configuration de base de donnees
    pause
    exit /b 1
)

echo.
echo 6. Execution des seeders...
php artisan db:seed
if %errorlevel% neq 0 (
    echo Erreur lors du seeding
    pause
    exit /b 1
)

echo.
echo 7. Publication de la configuration Sanctum...
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

echo.
echo ========================================
echo     Installation terminee avec succes !
echo ========================================
echo.
echo Comptes de test crees :
echo - Admin: <EMAIL> / password123
echo - Employes: <EMAIL>, <EMAIL>, <EMAIL> / password123
echo.
echo Pour demarrer le serveur :
echo php artisan serve
echo.
echo L'API sera accessible sur : http://localhost:8000/api
echo Documentation : Voir API_DOCUMENTATION.md
echo.
pause
