<?php

/**
 * Script de test API ClockIn - Simulation Postman
 * 
 * Ce script teste toutes les API du système ClockIn pour garantir
 * la bonne interaction avec la base de données MySQL.
 */

class ApiTester
{
    private $baseUrl;
    private $adminToken;
    private $employeeToken;
    private $testResults = [];
    private $createdEmployeeId;
    private $createdSiteId;

    public function __construct($baseUrl = 'http://localhost:8001/api')
    {
        $this->baseUrl = $baseUrl;
    }

    /**
     * Exécuter tous les tests
     */
    public function runAllTests()
    {
        echo "🧪 Démarrage des tests API ClockIn\n";
        echo "Base URL: {$this->baseUrl}\n\n";

        // Phase 1: Tests d'authentification
        $this->testAuthentication();

        // Phase 2: Tests gestion employés (Admin)
        $this->testEmployeeManagement();

        // Phase 3: Tests gestion sites (Admin)
        $this->testSiteManagement();

        // Phase 4: Tests pointage
        $this->testPointage();

        // Phase 5: Tests vérifications
        $this->testVerifications();

        // Afficher le résumé
        $this->displaySummary();
    }

    /**
     * Phase 1: Tests d'authentification
     */
    private function testAuthentication()
    {
        echo "🔐 Phase 1: Tests d'Authentification\n";
        echo "=====================================\n";

        // Test 1: Login Admin
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->makeRequest('POST', '/login', $loginData);
        $this->assertTest('POST /api/login', $response, 200, function($data) {
            return isset($data['data']['token']) && isset($data['data']['user']['role']) && $data['data']['user']['role'] === 'admin';
        });

        if ($response && isset($response['data']['token'])) {
            $this->adminToken = $response['data']['token'];
        }

        // Test 2: Profil utilisateur
        $response = $this->makeRequest('GET', '/me', null, $this->adminToken);
        $this->assertTest('GET /api/me', $response, 200, function($data) {
            return isset($data['data']['id']) && isset($data['data']['role']);
        });

        // Test 3: Logout
        $response = $this->makeRequest('POST', '/logout', null, $this->adminToken);
        $this->assertTest('POST /api/logout', $response, 200, function($data) {
            return $data['success'] === true;
        });

        // Re-login pour les tests suivants
        $response = $this->makeRequest('POST', '/login', $loginData);
        if ($response && isset($response['data']['token'])) {
            $this->adminToken = $response['data']['token'];
        }

        echo "\n";
    }

    /**
     * Phase 2: Tests gestion employés
     */
    private function testEmployeeManagement()
    {
        echo "👥 Phase 2: Tests Gestion Employés\n";
        echo "==================================\n";

        // Test 4: Liste employés
        $response = $this->makeRequest('GET', '/employees', null, $this->adminToken);
        $this->assertTest('GET /api/employees', $response, 200, function($data) {
            return isset($data['data']) && is_array($data['data']);
        });

        // Test 5: Créer employé
        $employeeData = [
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'employee'
        ];

        $response = $this->makeRequest('POST', '/employees', $employeeData, $this->adminToken);
        $this->assertTest('POST /api/employees', $response, 201, function($data) {
            return isset($data['data']['id']) && $data['data']['role'] === 'employee';
        });

        if ($response && isset($response['data']['id'])) {
            $this->createdEmployeeId = $response['data']['id'];
        }

        // Test 6: Détails employé
        if ($this->createdEmployeeId) {
            $response = $this->makeRequest('GET', "/employees/{$this->createdEmployeeId}", null, $this->adminToken);
            $this->assertTest('GET /api/employees/{id}', $response, 200, function($data) {
                return isset($data['data']['id']) && isset($data['data']['name']);
            });
        }

        // Test 7: Modifier employé
        if ($this->createdEmployeeId) {
            $updateData = [
                'name' => 'Test Employee Updated',
                'email' => '<EMAIL>'
            ];

            $response = $this->makeRequest('PUT', "/employees/{$this->createdEmployeeId}", $updateData, $this->adminToken);
            $this->assertTest('PUT /api/employees/{id}', $response, 200, function($data) {
                return $data['success'] === true;
            });
        }

        echo "\n";
    }

    /**
     * Phase 3: Tests gestion sites
     */
    private function testSiteManagement()
    {
        echo "🏗️ Phase 3: Tests Gestion Sites\n";
        echo "===============================\n";

        // Test 9: Liste sites
        $response = $this->makeRequest('GET', '/sites', null, $this->adminToken);
        $this->assertTest('GET /api/sites', $response, 200, function($data) {
            return isset($data['data']) && is_array($data['data']);
        });

        // Test 10: Créer site
        $siteData = [
            'name' => 'Test Site',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ];

        $response = $this->makeRequest('POST', '/sites', $siteData, $this->adminToken);
        $this->assertTest('POST /api/sites', $response, 201, function($data) {
            return isset($data['data']['id']) && isset($data['data']['name']);
        });

        if ($response && isset($response['data']['id'])) {
            $this->createdSiteId = $response['data']['id'];
        }

        // Test 11: Assigner site
        if ($this->createdSiteId && $this->createdEmployeeId) {
            $assignData = [
                'site_id' => $this->createdSiteId,
                'user_ids' => [$this->createdEmployeeId]
            ];

            $response = $this->makeRequest('POST', '/assign-site', $assignData, $this->adminToken);
            $this->assertTest('POST /api/assign-site', $response, 200, function($data) {
                return $data['success'] === true;
            });
        }

        echo "\n";
    }

    /**
     * Phase 4: Tests pointage
     */
    private function testPointage()
    {
        echo "⏰ Phase 4: Tests Pointage\n";
        echo "=========================\n";

        // Créer un token employé pour les tests de pointage
        $employeeLoginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->makeRequest('POST', '/login', $employeeLoginData);
        if ($response && isset($response['data']['token'])) {
            $this->employeeToken = $response['data']['token'];
        }

        // Test 13: Vérifier localisation
        if ($this->employeeToken && $this->createdSiteId) {
            $locationData = [
                'latitude' => 33.5731,
                'longitude' => -7.5898,
                'site_id' => $this->createdSiteId
            ];

            $response = $this->makeRequest('POST', '/check-location', $locationData, $this->employeeToken);
            $this->assertTest('POST /api/check-location', $response, 200, function($data) {
                return isset($data['data']['within_range']);
            });
        }

        // Test 14: Enregistrer pointage
        if ($this->employeeToken && $this->createdSiteId) {
            $pointageData = [
                'site_id' => $this->createdSiteId,
                'latitude' => 33.5731,
                'longitude' => -7.5898,
                'type' => 'debut'
            ];

            $response = $this->makeRequest('POST', '/save-pointage', $pointageData, $this->employeeToken);
            $this->assertTest('POST /api/save-pointage', $response, 201, function($data) {
                return isset($data['data']['id']) && isset($data['data']['debut_pointage']);
            });
        }

        // Test 15: Liste pointages (Admin)
        $response = $this->makeRequest('GET', '/pointages', null, $this->adminToken);
        $this->assertTest('GET /api/pointages', $response, 200, function($data) {
            return isset($data['data']) && is_array($data['data']);
        });

        echo "\n";
    }

    /**
     * Phase 5: Tests vérifications
     */
    private function testVerifications()
    {
        echo "📍 Phase 5: Tests Vérifications\n";
        echo "===============================\n";

        // Test 17: Vérifier localisation
        if ($this->employeeToken) {
            $verificationData = [
                'latitude' => 33.5731,
                'longitude' => -7.5898
            ];

            $response = $this->makeRequest('POST', '/verify-location', $verificationData, $this->employeeToken);
            $this->assertTest('POST /api/verify-location', $response, 201, function($data) {
                return isset($data['data']['id']) && isset($data['data']['latitude']);
            });
        }

        // Test 18: Historique vérifications (Admin)
        $response = $this->makeRequest('GET', '/verifications', null, $this->adminToken);
        $this->assertTest('GET /api/verifications', $response, 200, function($data) {
            return isset($data['data']) && is_array($data['data']);
        });

        echo "\n";
    }

    /**
     * Effectuer une requête HTTP
     */
    private function makeRequest($method, $endpoint, $data = null, $token = null)
    {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        if ($token) {
            $headers[] = "Authorization: Bearer $token";
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false) {
            return null;
        }

        $decodedResponse = json_decode($response, true);
        $decodedResponse['_http_code'] = $httpCode;

        return $decodedResponse;
    }

    /**
     * Vérifier un test
     */
    private function assertTest($testName, $response, $expectedCode, $validator = null)
    {
        $success = false;
        $message = '';

        if (!$response) {
            $message = 'Aucune réponse reçue';
        } elseif ($response['_http_code'] !== $expectedCode) {
            $message = "Code HTTP attendu: $expectedCode, reçu: {$response['_http_code']}";
        } elseif ($validator && !$validator($response)) {
            $message = 'Validation des données échouée';
        } else {
            $success = true;
            $message = 'Test réussi';
        }

        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'message' => $message,
            'http_code' => $response['_http_code'] ?? 'N/A'
        ];

        $status = $success ? '✅' : '❌';
        echo "$status $testName - $message\n";

        return $success;
    }

    /**
     * Afficher le résumé des tests
     */
    private function displaySummary()
    {
        echo "📊 Résumé des Tests\n";
        echo "==================\n";

        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($test) {
            return $test['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;

        echo "Total des tests: $total\n";
        echo "✅ Réussis: $passedCount\n";
        echo "❌ Échoués: $failedCount\n";
        echo "📈 Taux de réussite: " . round(($passedCount / $total) * 100, 2) . "%\n\n";

        if ($failedCount > 0) {
            echo "❌ Tests échoués:\n";
            foreach ($this->testResults as $test) {
                if (!$test['success']) {
                    echo "  - {$test['name']}: {$test['message']}\n";
                }
            }
        }

        // Nettoyage: supprimer les données de test
        $this->cleanup();
    }

    /**
     * Nettoyer les données de test
     */
    private function cleanup()
    {
        echo "\n🧹 Nettoyage des données de test...\n";

        // Supprimer l'employé de test
        if ($this->createdEmployeeId && $this->adminToken) {
            $this->makeRequest('DELETE', "/employees/{$this->createdEmployeeId}", null, $this->adminToken);
            echo "✅ Employé de test supprimé\n";
        }

        echo "✅ Nettoyage terminé\n";
    }
}

// Exécution des tests
if (php_sapi_name() === 'cli') {
    $tester = new ApiTester();
    $tester->runAllTests();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    echo "Usage: php test_api_postman.php\n";
}
