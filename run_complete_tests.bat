@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo    TESTS COMPLETS API CLOCKIN
echo ===============================================
echo.

:: Vérifier si PHP est disponible
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP n'est pas disponible dans le PATH
    echo Assurez-vous que WampServer est installé et configuré
    pause
    exit /b 1
)

echo ✅ PHP disponible
echo.

:: Vérifier la configuration Laravel
if not exist .env (
    echo ❌ Fichier .env manquant
    echo Copiez .env.example vers .env et configurez-le
    pause
    exit /b 1
)

echo ✅ Fichier .env trouvé
echo.

:: Générer la clé d'application si nécessaire
findstr /C:"APP_KEY=" .env | findstr /C:"APP_KEY=base64:" >nul
if errorlevel 1 (
    echo 🔑 Génération de la clé d'application...
    php artisan key:generate
    if errorlevel 1 (
        echo ❌ Erreur lors de la génération de la clé
        pause
        exit /b 1
    )
    echo ✅ Clé d'application générée
    echo.
)

:: Afficher la configuration de la base de données
echo 📊 Configuration de la base de données:
findstr "DB_" .env
echo.

:: Étape 1: Test de connectivité de base de données
echo ===============================================
echo ÉTAPE 1: TEST DE CONNECTIVITÉ DE BASE DE DONNÉES
echo ===============================================
echo.

echo Exécution du test de connectivité...
php artisan test tests/Feature/ExampleTest.php --verbose
set db_test_result=!errorlevel!

if !db_test_result! neq 0 (
    echo.
    echo ⚠️  Test de connectivité échoué
    echo Vérifiez que:
    echo - WampServer est démarré
    echo - MySQL est en cours d'exécution
    echo - La base de données 'clockin_db' existe
    echo.
    set /p continue="Continuer malgré l'erreur ? (o/n): "
    if /i "!continue!" neq "o" (
        pause
        exit /b 1
    )
) else (
    echo ✅ Test de connectivité réussi
)

echo.

:: Étape 2: Configuration de la base de données
echo ===============================================
echo ÉTAPE 2: CONFIGURATION DE LA BASE DE DONNÉES
echo ===============================================
echo.

echo Exécution du test de configuration...
php artisan test tests/Feature/DatabaseSetupTest.php --verbose
set setup_test_result=!errorlevel!

if !setup_test_result! neq 0 (
    echo.
    echo ⚠️  Test de configuration échoué
    echo.
    set /p continue="Continuer malgré l'erreur ? (o/n): "
    if /i "!continue!" neq "o" (
        pause
        exit /b 1
    )
) else (
    echo ✅ Configuration de la base de données réussie
)

echo.

:: Étape 3: Tests des APIs existants
echo ===============================================
echo ÉTAPE 3: TESTS DES APIS EXISTANTS
echo ===============================================
echo.

echo Test d'authentification...
php artisan test tests/Feature/AuthTest.php --verbose
set auth_test_result=!errorlevel!

echo.
echo Test de gestion des employés...
php artisan test tests/Feature/EmployeeTest.php --verbose
set employee_test_result=!errorlevel!

echo.
echo Test de pointage...
php artisan test tests/Feature/PointageTest.php --verbose
set pointage_test_result=!errorlevel!

:: Étape 4: Tests complets des APIs
echo.
echo ===============================================
echo ÉTAPE 4: TESTS COMPLETS DES APIS
echo ===============================================
echo.

echo Exécution des tests complets...
php artisan test tests/Feature/CompleteApiTest.php --verbose
set complete_test_result=!errorlevel!

:: Résumé des résultats
echo.
echo ===============================================
echo RÉSUMÉ DES TESTS
echo ===============================================
echo.

if !db_test_result! equ 0 (
    echo ✅ Connectivité de base de données: RÉUSSI
) else (
    echo ❌ Connectivité de base de données: ÉCHOUÉ
)

if !setup_test_result! equ 0 (
    echo ✅ Configuration de base de données: RÉUSSI
) else (
    echo ❌ Configuration de base de données: ÉCHOUÉ
)

if !auth_test_result! equ 0 (
    echo ✅ Tests d'authentification: RÉUSSI
) else (
    echo ❌ Tests d'authentification: ÉCHOUÉ
)

if !employee_test_result! equ 0 (
    echo ✅ Tests de gestion des employés: RÉUSSI
) else (
    echo ❌ Tests de gestion des employés: ÉCHOUÉ
)

if !pointage_test_result! equ 0 (
    echo ✅ Tests de pointage: RÉUSSI
) else (
    echo ❌ Tests de pointage: ÉCHOUÉ
)

if !complete_test_result! equ 0 (
    echo ✅ Tests complets des APIs: RÉUSSI
) else (
    echo ❌ Tests complets des APIs: ÉCHOUÉ
)

echo.

:: Calcul du score global
set /a total_tests=6
set /a passed_tests=0

if !db_test_result! equ 0 set /a passed_tests+=1
if !setup_test_result! equ 0 set /a passed_tests+=1
if !auth_test_result! equ 0 set /a passed_tests+=1
if !employee_test_result! equ 0 set /a passed_tests+=1
if !pointage_test_result! equ 0 set /a passed_tests+=1
if !complete_test_result! equ 0 set /a passed_tests+=1

echo 📊 Score global: !passed_tests!/!total_tests! tests réussis

if !passed_tests! equ !total_tests! (
    echo.
    echo 🎉 TOUS LES TESTS SONT RÉUSSIS !
    echo L'API ClockIn fonctionne correctement avec la base de données MySQL.
    echo.
    echo 💡 Vous pouvez maintenant:
    echo - Accéder à phpMyAdmin: http://localhost:8080/phpmyadmin
    echo - Tester l'API avec Postman
    echo - Consulter la documentation: http://localhost:8001/docs
) else (
    echo.
    echo ⚠️  CERTAINS TESTS ONT ÉCHOUÉ
    echo Vérifiez les erreurs ci-dessus et corrigez les problèmes.
    echo.
    echo 💡 Suggestions:
    echo - Vérifiez que WampServer est démarré
    echo - Vérifiez la configuration de la base de données
    echo - Consultez les logs Laravel dans storage/logs/
)

echo.
echo ===============================================
echo FIN DES TESTS
echo ===============================================

pause
