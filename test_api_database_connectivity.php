<?php

/**
 * Script de test complet pour l'API ClockIn
 * Teste la connectivité avec la base de données MySQL via phpMyAdmin
 * URL phpMyAdmin: http://localhost:8080/phpmyadmin/index.php?route=/database/structure&db=clockin_db
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use App\Models\Verification;
use App\Models\Log;

echo "🔍 === TEST COMPLET API CLOCKIN - CONNECTIVITÉ BASE DE DONNÉES ===\n\n";

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$baseUrl = 'http://localhost:8001/api';
$adminToken = '';
$employeeToken = '';
$testEmployeeId = '';
$testSiteId = '';

// Fonction pour faire des requêtes HTTP
function makeRequest($method, $url, $data = null, $token = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

// Fonction de test
function runTest($testName, $callback) {
    echo "🧪 Test: $testName\n";
    try {
        $result = $callback();
        if ($result) {
            echo "   ✅ RÉUSSI\n";
            return true;
        } else {
            echo "   ❌ ÉCHEC\n";
            return false;
        }
    } catch (Exception $e) {
        echo "   ❌ ERREUR: " . $e->getMessage() . "\n";
        return false;
    }
}

$passedTests = 0;
$totalTests = 0;

echo "📊 === TESTS DE CONNECTIVITÉ BASE DE DONNÉES ===\n\n";

// Test 1: Connexion à la base de données
$totalTests++;
$passedTests += runTest("Connexion à la base de données MySQL", function() {
    try {
        $connection = DB::connection()->getPdo();
        $dbName = DB::connection()->getDatabaseName();
        echo "   📍 Base de données connectée: $dbName\n";
        return true;
    } catch (Exception $e) {
        echo "   ❌ Erreur de connexion: " . $e->getMessage() . "\n";
        return false;
    }
});

// Test 2: Vérification des tables
$totalTests++;
$passedTests += runTest("Vérification des tables principales", function() {
    $tables = ['users', 'sites', 'pointages', 'assignments', 'verifications', 'logs'];
    foreach ($tables as $table) {
        $exists = DB::getSchemaBuilder()->hasTable($table);
        if (!$exists) {
            echo "   ❌ Table manquante: $table\n";
            return false;
        }
        echo "   ✅ Table trouvée: $table\n";
    }
    return true;
});

// Test 3: Vérification des données de test
$totalTests++;
$passedTests += runTest("Vérification des données de test", function() {
    $adminCount = User::where('role', 'admin')->count();
    $employeeCount = User::where('role', 'employee')->count();
    $siteCount = Site::count();
    
    echo "   📊 Admins: $adminCount, Employés: $employeeCount, Sites: $siteCount\n";
    
    return $adminCount > 0 && $employeeCount > 0 && $siteCount > 0;
});

echo "\n🌐 === TESTS API AVEC VALIDATION DB ===\n\n";

// Test 4: Login Admin
$totalTests++;
$passedTests += runTest("Login Admin - Authentification DB", function() use ($baseUrl, &$adminToken) {
    global $baseUrl, $adminToken;
    
    $response = makeRequest('POST', "$baseUrl/login", [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    if ($response['status'] === 200 && $response['body']['success']) {
        $adminToken = $response['body']['data']['token'];
        echo "   🔑 Token admin obtenu\n";
        echo "   👤 Utilisateur: " . $response['body']['data']['user']['name'] . "\n";
        return true;
    }
    
    echo "   ❌ Status: " . $response['status'] . "\n";
    return false;
});

// Test 5: Login Employee
$totalTests++;
$passedTests += runTest("Login Employee - Authentification DB", function() use ($baseUrl, &$employeeToken) {
    global $baseUrl, $employeeToken;
    
    $response = makeRequest('POST', "$baseUrl/login", [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    if ($response['status'] === 200 && $response['body']['success']) {
        $employeeToken = $response['body']['data']['token'];
        echo "   🔑 Token employé obtenu\n";
        echo "   👤 Utilisateur: " . $response['body']['data']['user']['name'] . "\n";
        return true;
    }
    
    echo "   ❌ Status: " . $response['status'] . "\n";
    return false;
});

// Test 6: Création d'employé
$totalTests++;
$passedTests += runTest("Création d'employé - Insert DB", function() use ($baseUrl, $adminToken, &$testEmployeeId) {
    global $baseUrl, $adminToken, $testEmployeeId;
    
    $response = makeRequest('POST', "$baseUrl/employees", [
        'name' => 'Test Employee API',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'role' => 'employee'
    ], $adminToken);
    
    if ($response['status'] === 201 && $response['body']['success']) {
        $testEmployeeId = $response['body']['data']['id'];
        echo "   👤 Employé créé avec ID: $testEmployeeId\n";
        echo "   📧 Email: " . $response['body']['data']['email'] . "\n";
        return true;
    }
    
    echo "   ❌ Status: " . $response['status'] . "\n";
    return false;
});

// Test 7: Création de site
$totalTests++;
$passedTests += runTest("Création de site - Insert DB avec GPS", function() use ($baseUrl, $adminToken, &$testSiteId) {
    global $baseUrl, $adminToken, $testSiteId;
    
    $response = makeRequest('POST', "$baseUrl/sites", [
        'name' => 'Site Test API',
        'latitude' => 33.5731,
        'longitude' => -7.5898
    ], $adminToken);
    
    if ($response['status'] === 201 && $response['body']['success']) {
        $testSiteId = $response['body']['data']['id'];
        echo "   🏗️ Site créé avec ID: $testSiteId\n";
        echo "   📍 GPS: " . $response['body']['data']['latitude'] . ", " . $response['body']['data']['longitude'] . "\n";
        return true;
    }
    
    echo "   ❌ Status: " . $response['status'] . "\n";
    return false;
});

// Test 8: Assignment site-employé
$totalTests++;
$passedTests += runTest("Assignment site-employé - Relation DB", function() use ($baseUrl, $adminToken, $testEmployeeId, $testSiteId) {
    global $baseUrl, $adminToken, $testEmployeeId, $testSiteId;
    
    $response = makeRequest('POST', "$baseUrl/assign-site", [
        'site_id' => (int)$testSiteId,
        'user_ids' => [(int)$testEmployeeId]
    ], $adminToken);
    
    if ($response['status'] === 200 && $response['body']['success']) {
        echo "   🔗 Assignment créé entre employé $testEmployeeId et site $testSiteId\n";
        return true;
    }
    
    echo "   ❌ Status: " . $response['status'] . "\n";
    return false;
});

// Test 9: Vérification des relations
$totalTests++;
$passedTests += runTest("Vérification des relations DB", function() use ($testEmployeeId, $testSiteId) {
    global $testEmployeeId, $testSiteId;
    
    // Vérifier que l'assignment existe
    $assignment = Assignment::where('user_id', $testEmployeeId)
                           ->where('site_id', $testSiteId)
                           ->first();
    
    if (!$assignment) {
        echo "   ❌ Assignment non trouvé en DB\n";
        return false;
    }
    
    // Vérifier les relations Eloquent
    $user = User::with('sites')->find($testEmployeeId);
    $site = Site::with('users')->find($testSiteId);
    
    if ($user->sites->count() === 0) {
        echo "   ❌ Relation user->sites non fonctionnelle\n";
        return false;
    }
    
    if ($site->users->count() === 0) {
        echo "   ❌ Relation site->users non fonctionnelle\n";
        return false;
    }
    
    echo "   ✅ Relations Eloquent fonctionnelles\n";
    echo "   📊 User a " . $user->sites->count() . " site(s)\n";
    echo "   📊 Site a " . $site->users->count() . " utilisateur(s)\n";
    
    return true;
});

echo "\n📊 === RÉSULTATS FINAUX ===\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 TOUS LES TESTS SONT RÉUSSIS ! ($passedTests/$totalTests)\n";
    echo "✅ La base de données clockin_db est correctement configurée\n";
    echo "✅ Toutes les tables et relations fonctionnent\n";
    echo "✅ Les modèles Eloquent sont opérationnels\n";
    echo "✅ Les APIs peuvent interagir avec la base de données\n";
    echo "✅ L'authentification Sanctum fonctionne\n";
} else {
    echo "⚠️  CERTAINS TESTS ONT ÉCHOUÉ ($passedTests/$totalTests)\n";
    echo "Vérifiez les erreurs ci-dessus\n";
}

echo "\n💡 ACCÈS AUX OUTILS:\n";
echo "- phpMyAdmin: http://localhost:8080/phpmyadmin\n";
echo "- Base de données: clockin_db\n";
echo "- API Base URL: http://localhost:8001/api\n";
echo "- Collection Postman: ClockIn_API_Complete_Tests.postman_collection.json\n";
echo "- Documentation: http://localhost:8001/docs\n";

echo "\n🔧 COMMANDES UTILES:\n";
echo "- Démarrer serveur: php artisan serve --port=8001\n";
echo "- Tests Laravel: php artisan test\n";
echo "- Migrations: php artisan migrate:fresh --seed\n";

?>
