<?php

/**
 * Script de diagnostic pour l'API ClockIn
 */

require_once 'vendor/autoload.php';

echo "🔍 === DIAGNOSTIC API CLOCKIN ===\n\n";

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "📋 1. Vérification des routes API\n";

// Lister les routes API
$routes = \Illuminate\Support\Facades\Route::getRoutes();
$apiRoutes = [];

foreach ($routes as $route) {
    $uri = $route->uri();
    if (strpos($uri, 'api/') === 0) {
        $apiRoutes[] = [
            'method' => implode('|', $route->methods()),
            'uri' => $uri,
            'name' => $route->getName(),
            'action' => $route->getActionName()
        ];
    }
}

echo "Nombre de routes API trouvées: " . count($apiRoutes) . "\n\n";

foreach ($apiRoutes as $route) {
    echo "  {$route['method']} /{$route['uri']} -> {$route['action']}\n";
}

echo "\n📋 2. Test de connectivité base de données\n";

try {
    $connection = \Illuminate\Support\Facades\DB::connection()->getPdo();
    $dbName = \Illuminate\Support\Facades\DB::connection()->getDatabaseName();
    echo "✅ Base de données connectée: $dbName\n";
    
    // Test des modèles
    $userCount = \App\Models\User::count();
    $siteCount = \App\Models\Site::count();
    echo "✅ Utilisateurs: $userCount\n";
    echo "✅ Sites: $siteCount\n";
    
} catch (Exception $e) {
    echo "❌ Erreur DB: " . $e->getMessage() . "\n";
}

echo "\n📋 3. Test d'authentification direct\n";

try {
    // Test direct du login
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        echo "✅ Utilisateur admin trouvé: {$user->name}\n";
        
        if (\Illuminate\Support\Facades\Hash::check('password123', $user->password)) {
            echo "✅ Mot de passe correct\n";
            
            // Créer un token
            $token = $user->createToken('test-token')->plainTextToken;
            echo "✅ Token créé: " . substr($token, 0, 20) . "...\n";
            
        } else {
            echo "❌ Mot de passe incorrect\n";
        }
    } else {
        echo "❌ Utilisateur admin non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur auth: " . $e->getMessage() . "\n";
}

echo "\n📋 4. Test des contrôleurs\n";

try {
    // Vérifier que les contrôleurs existent
    $controllers = [
        'App\Http\Controllers\Auth\AuthController',
        'App\Http\Controllers\Employee\EmployeeController',
        'App\Http\Controllers\Site\SiteController',
        'App\Http\Controllers\Pointage\PointageController',
        'App\Http\Controllers\Verification\VerificationController'
    ];
    
    foreach ($controllers as $controller) {
        if (class_exists($controller)) {
            echo "✅ Contrôleur trouvé: $controller\n";
        } else {
            echo "❌ Contrôleur manquant: $controller\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur contrôleurs: " . $e->getMessage() . "\n";
}

echo "\n📋 5. Configuration Laravel\n";

echo "APP_ENV: " . config('app.env') . "\n";
echo "APP_DEBUG: " . (config('app.debug') ? 'true' : 'false') . "\n";
echo "APP_URL: " . config('app.url') . "\n";
echo "DB_CONNECTION: " . config('database.default') . "\n";

echo "\n📋 6. Test de requête HTTP interne\n";

try {
    // Simuler une requête HTTP interne
    $request = \Illuminate\Http\Request::create('/api/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $request->headers->set('Content-Type', 'application/json');
    $request->headers->set('Accept', 'application/json');
    
    echo "✅ Requête créée pour /api/login\n";
    
} catch (Exception $e) {
    echo "❌ Erreur requête: " . $e->getMessage() . "\n";
}

echo "\n💡 RECOMMANDATIONS:\n";
echo "1. Vérifiez que le serveur Laravel est démarré sur le port 8001\n";
echo "2. Testez manuellement avec Postman: POST http://localhost:8001/api/login\n";
echo "3. Vérifiez les logs Laravel: tail -f storage/logs/laravel.log\n";
echo "4. Assurez-vous que les routes API sont bien définies\n";

?>
