<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin ClockIn',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
        ]);

        // Create employee users
        $employee1 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee',
        ]);

        $employee2 = User::create([
            'name' => 'Fatima Zahra',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee',
        ]);

        $employee3 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee',
        ]);

        // Create sites
        $site1 = Site::create([
            'name' => 'Chantier Casablanca Centre',
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);

        $site2 = Site::create([
            'name' => 'Chantier Rabat Agdal',
            'latitude' => 34.0209,
            'longitude' => -6.8416,
        ]);

        $site3 = Site::create([
            'name' => 'Chantier Marrakech Gueliz',
            'latitude' => 31.6295,
            'longitude' => -7.9811,
        ]);

        // Assign employees to sites
        Assignment::create(['user_id' => $employee1->id, 'site_id' => $site1->id]);
        Assignment::create(['user_id' => $employee1->id, 'site_id' => $site2->id]);
        Assignment::create(['user_id' => $employee2->id, 'site_id' => $site2->id]);
        Assignment::create(['user_id' => $employee2->id, 'site_id' => $site3->id]);
        Assignment::create(['user_id' => $employee3->id, 'site_id' => $site1->id]);
        Assignment::create(['user_id' => $employee3->id, 'site_id' => $site3->id]);

        $this->command->info('Database seeded successfully!');
        $this->command->info('Admin: <EMAIL> / password123');
        $this->command->info('Employees: <EMAIL>, <EMAIL>, <EMAIL> / password123');
    }
}
