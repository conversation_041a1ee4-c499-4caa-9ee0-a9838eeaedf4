# Guide de Tests API ClockIn

## Vue d'ensemble

Ce guide décrit comment analyser et tester les APIs ClockIn pour garantir une connexion et une interaction correctes avec la base de données MySQL `clockin_db`.

## Prérequis

### Environnement requis
- **WampServer** démarré avec MySQL actif
- **Base de données** `clockin_db` créée et accessible
- **phpMyAdmin** disponible sur http://localhost:8080/phpmyadmin
- **PHP** et **Composer** installés
- **Laravel** configuré avec le fichier `.env`

### Vérification de l'environnement
```bash
# Vérifier que WampServer est démarré
# Accéder à phpMyAdmin: http://localhost:8080/phpmyadmin

# Vérifier la configuration Laravel
php artisan --version
```

## Structure des Tests

### 1. Tests de Connectivité de Base de Données
**Fichier**: `tests/Feature/ExampleTest.php` (renommé en `DatabaseConnectivityTest`)

**Objectifs**:
- ✅ Tester la connexion MySQL de base
- ✅ Vérifier l'accès à la base de données `clockin_db`
- ✅ Lister les tables existantes
- ✅ Analyser les relations et contraintes

### 2. Tests de Configuration de Base de Données
**Fichier**: `tests/Feature/DatabaseSetupTest.php`

**Objectifs**:
- ✅ Exécuter les migrations automatiquement
- ✅ Vérifier la structure des tables
- ✅ Exécuter les seeders pour les données de test
- ✅ Valider les contraintes de clés étrangères

### 3. Tests Complets des APIs
**Fichier**: `tests/Feature/CompleteApiTest.php`

**Objectifs**:
- ✅ **Authentification**: Login, logout, profil utilisateur
- ✅ **Gestion des employés**: CRUD complet (admin uniquement)
- ✅ **Pointage**: Vérification GPS, enregistrement, consultation
- ✅ **Gestion des sites**: Création, attribution, consultation
- ✅ **Vérifications**: Demandes et historique
- ✅ **Sécurité**: Validation, autorisations, tokens
- ✅ **Intégrité**: Relations Eloquent, contraintes DB

## Exécution des Tests

### Méthode 1: Script Automatique (Recommandé)

#### Windows (WampServer)
```bash
# Exécuter tous les tests automatiquement
run_complete_tests.bat
```

#### Linux/Mac
```bash
# Rendre le script exécutable
chmod +x run_complete_tests.sh

# Exécuter tous les tests
./run_complete_tests.sh
```

### Méthode 2: Tests Individuels

#### Test de connectivité
```bash
php artisan test tests/Feature/ExampleTest.php --verbose
```

#### Test de configuration
```bash
php artisan test tests/Feature/DatabaseSetupTest.php --verbose
```

#### Tests existants
```bash
php artisan test tests/Feature/AuthTest.php --verbose
php artisan test tests/Feature/EmployeeTest.php --verbose
php artisan test tests/Feature/PointageTest.php --verbose
```

#### Tests complets
```bash
php artisan test tests/Feature/CompleteApiTest.php --verbose
```

### Méthode 3: Tous les tests Laravel
```bash
php artisan test --verbose
```

## Endpoints Testés

### 🔐 Authentification
- `POST /api/login` - Connexion utilisateur
- `POST /api/logout` - Déconnexion
- `GET /api/me` - Profil utilisateur

### 👥 Gestion des Employés (Admin)
- `GET /api/employees` - Liste des employés
- `POST /api/employees` - Créer un employé
- `GET /api/employees/{id}` - Consulter un employé
- `PUT /api/employees/{id}` - Modifier un employé
- `DELETE /api/employees/{id}` - Supprimer un employé

### ⏰ Pointage
- `POST /api/check-location` - Vérifier position GPS
- `POST /api/save-pointage` - Enregistrer un pointage
- `GET /api/pointages` - Liste des pointages (Admin)

### 🏗️ Gestion des Sites
- `GET /api/sites` - Liste des sites (Admin)
- `POST /api/sites` - Créer un site (Admin)
- `POST /api/assign-site` - Assigner un site (Admin)
- `GET /api/my-sites` - Sites assignés (Employé)

### 📍 Vérifications
- `POST /api/request-verification` - Demander vérification (Admin)
- `POST /api/verify-location` - Vérifier position
- `GET /api/verifications` - Historique (Admin)

## Structure de la Base de Données Testée

### Tables Principales
- **users**: Utilisateurs (admin/employés)
- **sites**: Chantiers avec coordonnées GPS
- **pointages**: Enregistrements de pointage
- **verifications**: Vérifications de localisation
- **assignments**: Attribution employés-chantiers
- **logs**: Logs de traçabilité

### Relations Testées
- `pointages.user_id → users.id`
- `pointages.site_id → sites.id`
- `verifications.user_id → users.id`
- `assignments.user_id → users.id`
- `assignments.site_id → sites.id`

## Données de Test

### Utilisateurs Créés
- **Admin**: `<EMAIL>` / `password123`
- **Employé**: `<EMAIL>` / `password123`

### Sites de Test
- **Site Test**: Casablanca (33.5731, -7.5898)

## Validation et Sécurité

### Tests de Validation
- ✅ Validation des emails
- ✅ Validation des mots de passe (minimum 6 caractères)
- ✅ Validation des coordonnées GPS (-90/90, -180/180)
- ✅ Validation des données requises

### Tests de Sécurité
- ✅ Authentification Sanctum
- ✅ Middleware admin pour routes sensibles
- ✅ Tokens Bearer valides
- ✅ Autorisations par rôle (admin vs employé)

## Résolution des Problèmes

### Erreur de Connexion à la Base de Données
```
❌ Database connection failed
```
**Solutions**:
1. Vérifiez que WampServer est démarré
2. Vérifiez que MySQL est actif (icône verte)
3. Vérifiez la configuration dans `.env`:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=clockin_db
   DB_USERNAME=root
   DB_PASSWORD=
   ```

### Base de Données Inexistante
```
❌ Database 'clockin_db' not found
```
**Solutions**:
1. Créez la base de données dans phpMyAdmin
2. Ou utilisez la commande SQL: `CREATE DATABASE clockin_db;`

### Tables Manquantes
```
⚠️ Tables don't exist yet. Need to run migrations.
```
**Solutions**:
1. Les tests exécutent automatiquement les migrations
2. Ou manuellement: `php artisan migrate --seed`

### Erreurs de Tests
```
❌ Tests failed
```
**Solutions**:
1. Consultez les logs détaillés dans la sortie des tests
2. Vérifiez les logs Laravel: `storage/logs/laravel.log`
3. Vérifiez la configuration des middlewares et routes

## Accès aux Outils

### phpMyAdmin
- **URL**: http://localhost:8080/phpmyadmin
- **Base de données**: clockin_db
- **Utilisateur**: root (sans mot de passe par défaut)

### Documentation API
- **URL**: http://localhost:8001/docs (après démarrage du serveur)
- **Collection Postman**: `ClockIn_API.postman_collection.json`

### Serveur Laravel
```bash
# Démarrer le serveur de développement
php artisan serve --port=8001
```

## Interprétation des Résultats

### ✅ Succès Complet
Tous les tests passent - L'API fonctionne correctement avec la base de données.

### ⚠️ Succès Partiel
Certains tests échouent - Vérifiez les erreurs spécifiques et corrigez.

### ❌ Échec Complet
Problème de configuration - Vérifiez l'environnement et la base de données.

## Support

Pour toute question ou problème:
1. Consultez les logs détaillés des tests
2. Vérifiez la configuration de l'environnement
3. Testez manuellement via phpMyAdmin
4. Utilisez la collection Postman pour les tests API
