<?php

/**
 * Script principal pour exécuter tous les tests et corriger les erreurs
 */

echo "🚀 LANCEMENT DES TESTS COMPLETS CLOCKIN API\n";
echo "============================================\n\n";

/**
 * Fonction pour exécuter une commande et retourner le résultat
 */
function runCommand($command) {
    echo "Exécution: {$command}\n";
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    return [
        'success' => $returnCode === 0,
        'output' => implode("\n", $output),
        'return_code' => $returnCode
    ];
}

/**
 * Vérifier si un service est accessible
 */
function checkService($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode >= 200 && $httpCode < 400;
}

/**
 * Vérifier les prérequis
 */
function checkPrerequisites() {
    echo "📋 VÉRIFICATION DES PRÉREQUIS\n";
    echo "==============================\n";
    
    $allGood = true;
    
    // Vérifier PHP
    if (version_compare(PHP_VERSION, '8.1.0', '>=')) {
        echo "✅ PHP " . PHP_VERSION . " (>= 8.1.0)\n";
    } else {
        echo "❌ PHP " . PHP_VERSION . " (< 8.1.0 requis)\n";
        $allGood = false;
    }
    
    // Vérifier Composer
    $composerResult = runCommand('composer --version');
    if ($composerResult['success']) {
        echo "✅ Composer installé\n";
    } else {
        echo "❌ Composer non trouvé\n";
        $allGood = false;
    }
    
    // Vérifier le fichier .env
    if (file_exists('.env')) {
        echo "✅ Fichier .env existe\n";
        
        // Vérifier la clé d'application
        $envContent = file_get_contents('.env');
        if (strpos($envContent, 'APP_KEY=base64:') !== false) {
            echo "✅ Clé d'application configurée\n";
        } else {
            echo "⚠️  Clé d'application manquante - génération...\n";
            $keyResult = runCommand('php artisan key:generate');
            if ($keyResult['success']) {
                echo "✅ Clé d'application générée\n";
            } else {
                echo "❌ Échec génération clé\n";
                $allGood = false;
            }
        }
    } else {
        echo "❌ Fichier .env manquant\n";
        if (file_exists('.env.example')) {
            echo "📋 Copie de .env.example vers .env...\n";
            copy('.env.example', '.env');
            echo "✅ Fichier .env créé\n";
        } else {
            $allGood = false;
        }
    }
    
    // Vérifier WampServer/MySQL
    if (checkService('http://localhost:8080/phpmyadmin')) {
        echo "✅ WampServer/phpMyAdmin accessible\n";
    } else {
        echo "❌ WampServer/phpMyAdmin non accessible\n";
        echo "💡 Démarrez WampServer et assurez-vous que phpMyAdmin fonctionne\n";
        $allGood = false;
    }
    
    echo "\n";
    return $allGood;
}

/**
 * Installer les dépendances si nécessaire
 */
function installDependencies() {
    echo "📦 INSTALLATION DES DÉPENDANCES\n";
    echo "================================\n";
    
    if (!file_exists('vendor/autoload.php')) {
        echo "Installation des dépendances Composer...\n";
        $result = runCommand('composer install');
        
        if ($result['success']) {
            echo "✅ Dépendances installées\n";
        } else {
            echo "❌ Échec installation dépendances\n";
            echo $result['output'] . "\n";
            return false;
        }
    } else {
        echo "✅ Dépendances déjà installées\n";
    }
    
    echo "\n";
    return true;
}

/**
 * Configurer la base de données
 */
function setupDatabase() {
    echo "🗄️  CONFIGURATION BASE DE DONNÉES\n";
    echo "===================================\n";
    
    // Vérifier la connexion à la base de données
    $dbResult = runCommand('php artisan migrate:status');
    
    if (!$dbResult['success']) {
        echo "⚠️  Base de données non accessible - tentative de migration...\n";
        
        // Essayer de créer/migrer la base de données
        $migrateResult = runCommand('php artisan migrate --force');
        
        if ($migrateResult['success']) {
            echo "✅ Migrations exécutées\n";
            
            // Exécuter les seeders
            $seedResult = runCommand('php artisan db:seed --force');
            if ($seedResult['success']) {
                echo "✅ Données de test créées\n";
            } else {
                echo "⚠️  Échec seeders (peut être normal si déjà exécutés)\n";
            }
        } else {
            echo "❌ Échec migrations\n";
            echo $migrateResult['output'] . "\n";
            return false;
        }
    } else {
        echo "✅ Base de données accessible\n";
    }
    
    echo "\n";
    return true;
}

/**
 * Démarrer le serveur Laravel
 */
function startLaravelServer() {
    echo "🌐 DÉMARRAGE SERVEUR LARAVEL\n";
    echo "=============================\n";
    
    // Vérifier si le serveur est déjà en cours d'exécution
    if (checkService('http://localhost:8001/api/../up')) {
        echo "✅ Serveur Laravel déjà en cours d'exécution sur le port 8001\n";
        echo "\n";
        return true;
    }
    
    echo "Démarrage du serveur Laravel sur le port 8001...\n";
    echo "💡 Le serveur restera en cours d'exécution en arrière-plan\n";
    echo "💡 Pour l'arrêter, utilisez Ctrl+C dans le terminal ou fermez cette fenêtre\n";
    
    // Démarrer le serveur en arrière-plan
    if (PHP_OS_FAMILY === 'Windows') {
        $command = 'start /B php artisan serve --port=8001';
    } else {
        $command = 'php artisan serve --port=8001 > /dev/null 2>&1 &';
    }
    
    exec($command);
    
    // Attendre que le serveur démarre
    echo "Attente du démarrage du serveur...\n";
    $attempts = 0;
    $maxAttempts = 10;
    
    while ($attempts < $maxAttempts) {
        sleep(2);
        if (checkService('http://localhost:8001/api/../up')) {
            echo "✅ Serveur Laravel démarré avec succès\n";
            echo "\n";
            return true;
        }
        $attempts++;
        echo "Tentative " . ($attempts + 1) . "/{$maxAttempts}...\n";
    }
    
    echo "❌ Échec du démarrage du serveur\n";
    echo "\n";
    return false;
}

// Exécution du script principal
echo "Début des tests à " . date('Y-m-d H:i:s') . "\n\n";

// Étape 1: Vérification des prérequis
if (!checkPrerequisites()) {
    echo "❌ Prérequis non satisfaits. Corrigez les erreurs ci-dessus.\n";
    exit(1);
}

// Étape 2: Installation des dépendances
if (!installDependencies()) {
    echo "❌ Échec installation dépendances.\n";
    exit(1);
}

// Étape 3: Configuration de la base de données
if (!setupDatabase()) {
    echo "❌ Échec configuration base de données.\n";
    exit(1);
}

// Étape 4: Démarrage du serveur Laravel
$serverStarted = startLaravelServer();

// Étape 5: Test de connectivité de la base de données
echo "🗄️  TEST CONNECTIVITÉ BASE DE DONNÉES\n";
echo "======================================\n";
echo "Exécution du test de connectivité...\n";
$dbTestResult = runCommand('php test_database_connectivity.php');
echo $dbTestResult['output'] . "\n";

if (!$dbTestResult['success']) {
    echo "❌ Tests de base de données échoués\n";
    exit(1);
}

// Étape 6: Tests API complets
if ($serverStarted) {
    echo "🚀 TESTS API COMPLETS\n";
    echo "======================\n";
    echo "Exécution des tests API...\n";
    
    // Attendre un peu pour que le serveur soit complètement prêt
    sleep(3);
    
    $apiTestResult = runCommand('php test_api_complete.php');
    echo $apiTestResult['output'] . "\n";
    
    if (!$apiTestResult['success']) {
        echo "❌ Tests API échoués\n";
    }
} else {
    echo "⚠️  Tests API ignorés - serveur non démarré\n";
}

// Résumé final
echo "\n🎯 RÉSUMÉ FINAL\n";
echo "===============\n";
echo "✅ Prérequis vérifiés\n";
echo "✅ Dépendances installées\n";
echo "✅ Base de données configurée\n";
echo ($serverStarted ? "✅" : "❌") . " Serveur Laravel\n";
echo "✅ Tests de connectivité DB exécutés\n";
echo ($serverStarted ? "✅" : "⚠️ ") . " Tests API exécutés\n";

echo "\n💡 ACCÈS RAPIDE:\n";
echo "- phpMyAdmin: http://localhost:8080/phpmyadmin\n";
echo "- Base de données: clockin_db\n";
if ($serverStarted) {
    echo "- API ClockIn: http://localhost:8001/api\n";
    echo "- Documentation: http://localhost:8001/docs\n";
}
echo "- Collection Postman: ClockIn_API_Tests.postman_collection.json\n";

echo "\n📋 PROCHAINES ÉTAPES:\n";
echo "1. Importez la collection Postman pour tester manuellement\n";
echo "2. Vérifiez la base de données dans phpMyAdmin\n";
echo "3. Testez les endpoints avec Postman ou curl\n";

if ($serverStarted) {
    echo "\n⚠️  IMPORTANT: Le serveur Laravel reste en cours d'exécution\n";
    echo "Pour l'arrêter, fermez cette fenêtre ou utilisez Ctrl+C\n";
}
