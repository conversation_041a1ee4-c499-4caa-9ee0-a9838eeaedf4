<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmployeeRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class EmployeeController extends Controller
{
    /**
     * Display a listing of employees
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();
        
        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Filter by role
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }
        
        // Order by name
        $query->orderBy('name');
        
        // Paginate results
        $employees = $query->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => UserResource::collection($employees->items()),
            'pagination' => [
                'current_page' => $employees->currentPage(),
                'last_page' => $employees->lastPage(),
                'per_page' => $employees->perPage(),
                'total' => $employees->total(),
                'from' => $employees->firstItem(),
                'to' => $employees->lastItem(),
            ]
        ]);
    }
    
    /**
     * Store a newly created employee
     */
    public function store(EmployeeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        // Hash password
        $validated['password'] = Hash::make($validated['password']);
        
        try {
            $employee = User::create($validated);
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee created successfully',
                    'fr' => 'Employé créé avec succès',
                    'ar' => 'تم إنشاء الموظف بنجاح'
                ],
                'data' => new UserResource($employee)
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to create employee',
                    'fr' => 'Échec de la création de l\'employé',
                    'ar' => 'فشل في إنشاء الموظف'
                ]
            ], 500);
        }
    }
    
    /**
     * Display the specified employee
     */
    public function show(int $id): JsonResponse
    {
        try {
            $employee = User::with(['sites', 'pointages.site'])->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => new UserResource($employee)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Employee not found',
                    'fr' => 'Employé non trouvé',
                    'ar' => 'الموظف غير موجود'
                ]
            ], 404);
        }
    }

    /**
     * Update the specified employee
     */
    public function update(EmployeeRequest $request, int $id): JsonResponse
    {
        try {
            $employee = User::findOrFail($id);
            $validated = $request->validated();

            // Hash password if provided
            if (isset($validated['password'])) {
                $validated['password'] = Hash::make($validated['password']);
            }

            $employee->update($validated);

            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee updated successfully',
                    'fr' => 'Employé mis à jour avec succès',
                    'ar' => 'تم تحديث الموظف بنجاح'
                ],
                'data' => new UserResource($employee->fresh())
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to update employee',
                    'fr' => 'Échec de la mise à jour de l\'employé',
                    'ar' => 'فشل في تحديث الموظف'
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified employee
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $employee = User::findOrFail($id);

            // Prevent deleting the last admin
            if ($employee->isAdmin() && User::where('role', 'admin')->count() <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => [
                        'en' => 'Cannot delete the last admin user',
                        'fr' => 'Impossible de supprimer le dernier utilisateur admin',
                        'ar' => 'لا يمكن حذف آخر مستخدم مدير'
                    ]
                ], 400);
            }

            $employee->delete();

            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee deleted successfully',
                    'fr' => 'Employé supprimé avec succès',
                    'ar' => 'تم حذف الموظف بنجاح'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to delete employee',
                    'fr' => 'Échec de la suppression de l\'employé',
                    'ar' => 'فشل في حذف الموظف'
                ]
            ], 500);
        }
    }
}
