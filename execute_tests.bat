@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo     EXÉCUTION DES TESTS CLOCKIN
echo ===============================================
echo.

:: Vérifier PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP non disponible
    pause
    exit /b 1
)

echo ✅ PHP disponible
echo.

:: Vérifier les fichiers
if not exist .env (
    echo ❌ Fichier .env manquant
    pause
    exit /b 1
)

if not exist artisan (
    echo ❌ Fichier artisan manquant
    pause
    exit /b 1
)

echo ✅ Fichiers Laravel trouvés
echo.

:: Afficher la configuration
echo 📊 Configuration de la base de données:
findstr "DB_" .env
echo.

:: Générer la clé si nécessaire
findstr /C:"APP_KEY=base64:" .env >nul
if errorlevel 1 (
    echo 🔑 Génération de la clé d'application...
    php artisan key:generate --quiet
    echo ✅ Clé générée
    echo.
)

echo ===============================================
echo ÉTAPE 1: TEST DE CONNEXION À LA BASE DE DONNÉES
echo ===============================================
echo.

echo Exécution du test de connexion...
php artisan test tests/Feature/DatabaseConnectionTest.php --verbose

if errorlevel 1 (
    echo.
    echo ❌ Test de connexion échoué
    echo 💡 Vérifiez que:
    echo    - WampServer est démarré
    echo    - MySQL est actif
    echo    - La base de données 'clockin_db' existe
    echo    - La configuration .env est correcte
    echo.
    set /p continue="Continuer malgré l'erreur ? (o/n): "
    if /i "!continue!" neq "o" (
        pause
        exit /b 1
    )
) else (
    echo.
    echo ✅ Test de connexion réussi
)

echo.
echo ===============================================
echo ÉTAPE 2: TEST COMPLET DES APIS
echo ===============================================
echo.

echo Exécution des tests d'API...
php artisan test tests/Feature/AllApiEndpointsTest.php --verbose

if errorlevel 1 (
    echo.
    echo ❌ Certains tests d'API ont échoué
    echo 💡 Vérifiez les erreurs ci-dessus
) else (
    echo.
    echo ✅ Tous les tests d'API ont réussi
)

echo.
echo ===============================================
echo ÉTAPE 3: TESTS EXISTANTS
echo ===============================================
echo.

echo Test d'authentification...
php artisan test tests/Feature/AuthTest.php --verbose

echo.
echo Test de gestion des employés...
php artisan test tests/Feature/EmployeeTest.php --verbose

echo.
echo Test de pointage...
php artisan test tests/Feature/PointageTest.php --verbose

echo.
echo ===============================================
echo ÉTAPE 4: TOUS LES TESTS
echo ===============================================
echo.

echo Exécution de tous les tests...
php artisan test --verbose

echo.
echo ===============================================
echo RÉSUMÉ
echo ===============================================
echo.

echo 🎯 Tests exécutés:
echo    1. ✅ Test de connexion à la base de données
echo    2. ✅ Test complet des APIs
echo    3. ✅ Tests d'authentification
echo    4. ✅ Tests de gestion des employés
echo    5. ✅ Tests de pointage
echo    6. ✅ Suite complète de tests
echo.

echo 💡 Liens utiles:
echo    - phpMyAdmin: http://localhost:8080/phpmyadmin
echo    - Base de données: clockin_db
echo    - API: http://localhost:8001/api
echo.

echo 📋 Fichiers de test créés:
echo    - tests/Feature/DatabaseConnectionTest.php
echo    - tests/Feature/AllApiEndpointsTest.php
echo    - tests/Feature/CompleteApiTest.php
echo    - TESTS_API_GUIDE.md
echo.

pause
