# 🚀 Guide de Test Postman - API ClockIn

## 📋 Configuration Initiale

### 1. Importer la Collection
1. <PERSON><PERSON><PERSON><PERSON>r <PERSON>man
2. <PERSON><PERSON><PERSON> sur "Import"
3. <PERSON><PERSON><PERSON><PERSON><PERSON> le fichier `ClockIn_API_Complete_Tests.postman_collection.json`
4. Confirmer l'importation

### 2. Vérifier les Variables
- `base_url`: http://localhost:8001/api
- `admin_token`: (sera rempli automatiquement)
- `employee_token`: (sera rempli automatiquement)
- `test_employee_id`: (sera rempli automatiquement)
- `test_site_id`: (sera rempli automatiquement)

## 🧪 Séquence de Tests Recommandée

### Phase 1: Tests d'Authentification
1. **Login Admin - DB Connectivity Test**
   - Méthode: POST
   - URL: {{base_url}}/login
   - Body: 
   ```json
   {
       "email": "<EMAIL>",
       "password": "password123"
   }
   ```
   - ✅ Attendu: Status 200, token généré

2. **<PERSON>gin Employee - DB Test**
   - Méthode: POST
   - URL: {{base_url}}/login
   - Body:
   ```json
   {
       "email": "<EMAIL>",
       "password": "password123"
   }
   ```
   - ✅ Attendu: Status 200, token employé

3. **Get Profile - Sanctum Auth Test**
   - Méthode: GET
   - URL: {{base_url}}/me
   - Headers: Authorization: Bearer {{admin_token}}
   - ✅ Attendu: Status 200, profil admin

### Phase 2: Tests CRUD Employés
4. **List Employees - DB Query Test**
   - Méthode: GET
   - URL: {{base_url}}/employees?per_page=10
   - Headers: Authorization: Bearer {{admin_token}}
   - ✅ Attendu: Status 200, liste avec pagination

5. **Create Employee - DB Insert Test**
   - Méthode: POST
   - URL: {{base_url}}/employees
   - Headers: Authorization: Bearer {{admin_token}}
   - Body:
   ```json
   {
       "name": "Test Employee DB",
       "email": "<EMAIL>",
       "password": "password123",
       "role": "employee"
   }
   ```
   - ✅ Attendu: Status 201, employé créé

### Phase 3: Tests Sites et Relations
6. **List Sites - DB Query Test**
   - Méthode: GET
   - URL: {{base_url}}/sites
   - Headers: Authorization: Bearer {{admin_token}}
   - ✅ Attendu: Status 200, sites avec GPS

7. **Create Site - DB Insert Test**
   - Méthode: POST
   - URL: {{base_url}}/sites
   - Headers: Authorization: Bearer {{admin_token}}
   - Body:
   ```json
   {
       "name": "Test Site DB",
       "latitude": 33.5731,
       "longitude": -7.5898
   }
   ```
   - ✅ Attendu: Status 201, site créé

8. **Assign Site - DB Relation Test**
   - Méthode: POST
   - URL: {{base_url}}/assign-site
   - Headers: Authorization: Bearer {{admin_token}}
   - Body:
   ```json
   {
       "site_id": {{test_site_id}},
       "user_ids": [{{test_employee_id}}]
   }
   ```
   - ✅ Attendu: Status 200, relation créée

### Phase 4: Tests Pointage
9. **Check Location - GPS Validation**
   - Méthode: POST
   - URL: {{base_url}}/check-location
   - Headers: Authorization: Bearer {{employee_token}}
   - Body:
   ```json
   {
       "site_id": {{test_site_id}},
       "latitude": 33.5731,
       "longitude": -7.5898
   }
   ```
   - ✅ Attendu: Status 200, validation GPS

10. **Save Pointage - DB Insert Complex**
    - Méthode: POST
    - URL: {{base_url}}/save-pointage
    - Headers: Authorization: Bearer {{employee_token}}
    - Body:
    ```json
    {
        "user_id": {{test_employee_id}},
        "site_id": {{test_site_id}},
        "debut_pointage": "2024-01-15 08:00:00",
        "fin_pointage": "2024-01-15 17:00:00",
        "debut_latitude": 33.5731,
        "debut_longitude": -7.5898,
        "fin_latitude": 33.5731,
        "fin_longitude": -7.5898
    }
    ```
    - ✅ Attendu: Status 201, pointage enregistré

## 🔍 Validation Base de Données

### Vérifications phpMyAdmin
Après chaque test, vérifiez dans phpMyAdmin (http://localhost:8080/phpmyadmin):

1. **Table users**: Nouveaux employés créés
2. **Table sites**: Nouveaux sites avec coordonnées GPS
3. **Table assignments**: Relations employé-site
4. **Table pointages**: Enregistrements de pointage
5. **Table logs**: Logs d'activité

### Requêtes SQL de Vérification
```sql
-- Vérifier les utilisateurs
SELECT id, name, email, role FROM users ORDER BY created_at DESC;

-- Vérifier les sites
SELECT id, name, latitude, longitude FROM sites ORDER BY created_at DESC;

-- Vérifier les assignments
SELECT a.id, u.name as user_name, s.name as site_name 
FROM assignments a 
JOIN users u ON a.user_id = u.id 
JOIN sites s ON a.site_id = s.id;

-- Vérifier les pointages
SELECT p.id, u.name, s.name, p.debut_pointage, p.fin_pointage 
FROM pointages p 
JOIN users u ON p.user_id = u.id 
JOIN sites s ON p.site_id = s.id 
ORDER BY p.created_at DESC;
```

## 🚨 Dépannage

### Erreurs Communes

1. **Status 500 - Erreur Serveur**
   - Vérifiez les logs Laravel: `tail -f storage/logs/laravel.log`
   - Vérifiez la configuration .env
   - Redémarrez le serveur: `php artisan serve --port=8001`

2. **Status 401 - Non Autorisé**
   - Vérifiez que le token est bien défini
   - Relancez le login admin/employé
   - Vérifiez l'en-tête Authorization

3. **Status 422 - Validation Error**
   - Vérifiez les données envoyées
   - Consultez les règles de validation
   - Vérifiez les contraintes de base de données

4. **Status 404 - Route Non Trouvée**
   - Vérifiez l'URL de base
   - Vérifiez que le serveur est démarré
   - Consultez les routes: `php artisan route:list`

### Tests de Connectivité

```bash
# Tester la connectivité serveur
curl -I http://localhost:8001

# Tester une route API
curl -X POST http://localhost:8001/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 📊 Résultats Attendus

### Tests Réussis
- ✅ Tous les status codes corrects (200, 201, 401, 422)
- ✅ Tokens générés et utilisés
- ✅ Données créées en base
- ✅ Relations fonctionnelles
- ✅ Validation GPS opérationnelle

### Validation Finale
1. **Base de données**: Toutes les tables peuplées
2. **Authentification**: Tokens Sanctum fonctionnels
3. **CRUD**: Opérations complètes
4. **Relations**: Many-to-many opérationnelles
5. **GPS**: Calculs de distance fonctionnels

## 🎯 Prochaines Étapes

Après validation complète:
1. Tests de charge avec Postman Runner
2. Tests de sécurité (injections, XSS)
3. Tests de performance
4. Documentation automatique avec Scribe
5. Préparation déploiement production
