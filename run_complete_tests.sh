#!/bin/bash

echo "==============================================="
echo "    TESTS COMPLETS API CLOCKIN"
echo "==============================================="
echo

# Vérifier si PHP est disponible
if ! command -v php &> /dev/null; then
    echo "❌ PHP n'est pas disponible dans le PATH"
    echo "Assurez-vous que PHP est installé et configuré"
    exit 1
fi

echo "✅ PHP disponible"
echo

# Vérifier la configuration Laravel
if [ ! -f .env ]; then
    echo "❌ Fichier .env manquant"
    echo "Copiez .env.example vers .env et configurez-le"
    exit 1
fi

echo "✅ Fichier .env trouvé"
echo

# Générer la clé d'application si nécessaire
if ! grep -q "APP_KEY=base64:" .env; then
    echo "🔑 Génération de la clé d'application..."
    php artisan key:generate
    if [ $? -ne 0 ]; then
        echo "❌ Erreur lors de la génération de la clé"
        exit 1
    fi
    echo "✅ Clé d'application générée"
    echo
fi

# Afficher la configuration de la base de données
echo "📊 Configuration de la base de données:"
grep "DB_" .env
echo

# Étape 1: Test de connectivité de base de données
echo "==============================================="
echo "ÉTAPE 1: TEST DE CONNECTIVITÉ DE BASE DE DONNÉES"
echo "==============================================="
echo

echo "Exécution du test de connectivité..."
php artisan test tests/Feature/ExampleTest.php --verbose
db_test_result=$?

if [ $db_test_result -ne 0 ]; then
    echo
    echo "⚠️  Test de connectivité échoué"
    echo "Vérifiez que:"
    echo "- Le serveur MySQL est démarré"
    echo "- La base de données 'clockin_db' existe"
    echo "- Les paramètres de connexion sont corrects"
    echo
    read -p "Continuer malgré l'erreur ? (o/n): " continue
    if [ "$continue" != "o" ] && [ "$continue" != "O" ]; then
        exit 1
    fi
else
    echo "✅ Test de connectivité réussi"
fi

echo

# Étape 2: Configuration de la base de données
echo "==============================================="
echo "ÉTAPE 2: CONFIGURATION DE LA BASE DE DONNÉES"
echo "==============================================="
echo

echo "Exécution du test de configuration..."
php artisan test tests/Feature/DatabaseSetupTest.php --verbose
setup_test_result=$?

if [ $setup_test_result -ne 0 ]; then
    echo
    echo "⚠️  Test de configuration échoué"
    echo
    read -p "Continuer malgré l'erreur ? (o/n): " continue
    if [ "$continue" != "o" ] && [ "$continue" != "O" ]; then
        exit 1
    fi
else
    echo "✅ Configuration de la base de données réussie"
fi

echo

# Étape 3: Tests des APIs existants
echo "==============================================="
echo "ÉTAPE 3: TESTS DES APIS EXISTANTS"
echo "==============================================="
echo

echo "Test d'authentification..."
php artisan test tests/Feature/AuthTest.php --verbose
auth_test_result=$?

echo
echo "Test de gestion des employés..."
php artisan test tests/Feature/EmployeeTest.php --verbose
employee_test_result=$?

echo
echo "Test de pointage..."
php artisan test tests/Feature/PointageTest.php --verbose
pointage_test_result=$?

# Étape 4: Tests complets des APIs
echo
echo "==============================================="
echo "ÉTAPE 4: TESTS COMPLETS DES APIS"
echo "==============================================="
echo

echo "Exécution des tests complets..."
php artisan test tests/Feature/CompleteApiTest.php --verbose
complete_test_result=$?

# Résumé des résultats
echo
echo "==============================================="
echo "RÉSUMÉ DES TESTS"
echo "==============================================="
echo

if [ $db_test_result -eq 0 ]; then
    echo "✅ Connectivité de base de données: RÉUSSI"
else
    echo "❌ Connectivité de base de données: ÉCHOUÉ"
fi

if [ $setup_test_result -eq 0 ]; then
    echo "✅ Configuration de base de données: RÉUSSI"
else
    echo "❌ Configuration de base de données: ÉCHOUÉ"
fi

if [ $auth_test_result -eq 0 ]; then
    echo "✅ Tests d'authentification: RÉUSSI"
else
    echo "❌ Tests d'authentification: ÉCHOUÉ"
fi

if [ $employee_test_result -eq 0 ]; then
    echo "✅ Tests de gestion des employés: RÉUSSI"
else
    echo "❌ Tests de gestion des employés: ÉCHOUÉ"
fi

if [ $pointage_test_result -eq 0 ]; then
    echo "✅ Tests de pointage: RÉUSSI"
else
    echo "❌ Tests de pointage: ÉCHOUÉ"
fi

if [ $complete_test_result -eq 0 ]; then
    echo "✅ Tests complets des APIs: RÉUSSI"
else
    echo "❌ Tests complets des APIs: ÉCHOUÉ"
fi

echo

# Calcul du score global
total_tests=6
passed_tests=0

[ $db_test_result -eq 0 ] && ((passed_tests++))
[ $setup_test_result -eq 0 ] && ((passed_tests++))
[ $auth_test_result -eq 0 ] && ((passed_tests++))
[ $employee_test_result -eq 0 ] && ((passed_tests++))
[ $pointage_test_result -eq 0 ] && ((passed_tests++))
[ $complete_test_result -eq 0 ] && ((passed_tests++))

echo "📊 Score global: $passed_tests/$total_tests tests réussis"

if [ $passed_tests -eq $total_tests ]; then
    echo
    echo "🎉 TOUS LES TESTS SONT RÉUSSIS !"
    echo "L'API ClockIn fonctionne correctement avec la base de données MySQL."
    echo
    echo "💡 Vous pouvez maintenant:"
    echo "- Accéder à phpMyAdmin: http://localhost:8080/phpmyadmin"
    echo "- Tester l'API avec Postman"
    echo "- Consulter la documentation: http://localhost:8001/docs"
else
    echo
    echo "⚠️  CERTAINS TESTS ONT ÉCHOUÉ"
    echo "Vérifiez les erreurs ci-dessus et corrigez les problèmes."
    echo
    echo "💡 Suggestions:"
    echo "- Vérifiez que le serveur MySQL est démarré"
    echo "- Vérifiez la configuration de la base de données"
    echo "- Consultez les logs Laravel dans storage/logs/"
fi

echo
echo "==============================================="
echo "FIN DES TESTS"
echo "==============================================="
