<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pointage>
 */
class PointageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $debutPointage = fake()->dateTimeBetween('-1 month', 'now');
        $finPointage = Carbon::parse($debutPointage)->addHours(fake()->numberBetween(6, 10));
        
        return [
            'user_id' => User::factory(),
            'site_id' => Site::factory(),
            'debut_pointage' => $debutPointage,
            'fin_pointage' => $finPointage,
            'debut_latitude' => fake()->latitude(30, 36),
            'debut_longitude' => fake()->longitude(-12, -1),
            'fin_latitude' => fake()->latitude(30, 36),
            'fin_longitude' => fake()->longitude(-12, -1),
        ];
    }
}
