<?php

/**
 * Test rapide de l'API ClockIn
 */

echo "🚀 === TEST RAPIDE API CLOCKIN ===\n\n";

$baseUrl = 'http://localhost:8001/api';

// Fonction pour faire des requêtes HTTP
function quickRequest($method, $url, $data = null, $token = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => $response ? json_decode($response, true) : null,
        'raw' => $response,
        'error' => $error
    ];
}

echo "1. Test de connectivité serveur...\n";
$response = quickRequest('GET', 'http://localhost:8001');
if ($response['status'] === 200) {
    echo "   ✅ Serveur Laravel accessible\n";
} else {
    echo "   ❌ Serveur non accessible (Status: {$response['status']})\n";
    if ($response['error']) {
        echo "   Erreur: {$response['error']}\n";
    }
    exit(1);
}

echo "\n2. Test login admin...\n";
$loginResponse = quickRequest('POST', "$baseUrl/login", [
    'email' => '<EMAIL>',
    'password' => 'password123'
]);

echo "   Status: {$loginResponse['status']}\n";
if ($loginResponse['body']) {
    if (isset($loginResponse['body']['success']) && $loginResponse['body']['success']) {
        echo "   ✅ Login réussi\n";
        $token = $loginResponse['body']['data']['token'];
        echo "   🔑 Token obtenu: " . substr($token, 0, 20) . "...\n";
        
        echo "\n3. Test profil utilisateur...\n";
        $profileResponse = quickRequest('GET', "$baseUrl/me", null, $token);
        echo "   Status: {$profileResponse['status']}\n";
        
        if ($profileResponse['body'] && isset($profileResponse['body']['success']) && $profileResponse['body']['success']) {
            echo "   ✅ Profil récupéré\n";
            echo "   👤 Utilisateur: " . $profileResponse['body']['data']['name'] . "\n";
            echo "   📧 Email: " . $profileResponse['body']['data']['email'] . "\n";
            echo "   🔐 Rôle: " . $profileResponse['body']['data']['role'] . "\n";
        } else {
            echo "   ❌ Échec récupération profil\n";
            if ($profileResponse['body']) {
                echo "   Réponse: " . json_encode($profileResponse['body']) . "\n";
            }
        }
        
        echo "\n4. Test liste employés...\n";
        $employeesResponse = quickRequest('GET', "$baseUrl/employees", null, $token);
        echo "   Status: {$employeesResponse['status']}\n";
        
        if ($employeesResponse['body'] && isset($employeesResponse['body']['success']) && $employeesResponse['body']['success']) {
            echo "   ✅ Liste employés récupérée\n";
            echo "   📊 Nombre d'employés: " . count($employeesResponse['body']['data']) . "\n";
        } else {
            echo "   ❌ Échec récupération employés\n";
        }
        
    } else {
        echo "   ❌ Login échoué\n";
        echo "   Réponse: " . json_encode($loginResponse['body']) . "\n";
    }
} else {
    echo "   ❌ Pas de réponse JSON\n";
    echo "   Réponse brute: " . substr($loginResponse['raw'], 0, 200) . "...\n";
}

echo "\n📊 === RÉSUMÉ ===\n";
echo "- Serveur Laravel: " . ($response['status'] === 200 ? "✅ OK" : "❌ KO") . "\n";
echo "- API Login: " . (isset($loginResponse['body']['success']) && $loginResponse['body']['success'] ? "✅ OK" : "❌ KO") . "\n";
echo "- Authentification: " . (isset($profileResponse['body']['success']) && $profileResponse['body']['success'] ? "✅ OK" : "❌ KO") . "\n";

echo "\n💡 Pour tester avec Postman:\n";
echo "1. Importer: ClockIn_API_Complete_Tests.postman_collection.json\n";
echo "2. Configurer base_url: http://localhost:8001/api\n";
echo "3. Exécuter les tests dans l'ordre\n";
echo "4. Vérifier phpMyAdmin: http://localhost:8080/phpmyadmin\n";

?>
