<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Pointage\PointageController;
use App\Http\Controllers\Employee\EmployeeController;
use App\Http\Controllers\Site\SiteController;
use App\Http\Controllers\Verification\VerificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login'])->name('api.login');

// Protected routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Authentication routes
    Route::post('/logout', [AuthController::class, 'logout'])->name('api.logout');
    Route::get('/me', [AuthController::class, 'me'])->name('api.me');
    
    // Pointage routes
    Route::post('/check-location', [PointageController::class, 'checkLocation'])->name('api.check-location');
    Route::post('/save-pointage', [PointageController::class, 'savePointage'])->name('api.save-pointage');
    
    // Verification routes
    Route::post('/verify-location', [VerificationController::class, 'verifyLocation'])->name('api.verify-location');
    
    // User's sites
    Route::get('/my-sites', [SiteController::class, 'mySites'])->name('api.my-sites');
    
    // Admin only routes
    Route::middleware(['admin'])->group(function () {
        
        // Pointage management
        Route::get('/pointages', [PointageController::class, 'index'])->name('api.pointages.index');
        
        // Employee management (CRUD)
        Route::get('/employees', [EmployeeController::class, 'index'])->name('api.employees.index');
        Route::post('/employees', [EmployeeController::class, 'store'])->name('api.employees.store');
        Route::get('/employees/{id}', [EmployeeController::class, 'show'])->name('api.employees.show');
        Route::put('/employees/{id}', [EmployeeController::class, 'update'])->name('api.employees.update');
        Route::delete('/employees/{id}', [EmployeeController::class, 'destroy'])->name('api.employees.destroy');
        
        // Site management
        Route::get('/sites', [SiteController::class, 'index'])->name('api.sites.index');
        Route::post('/sites', [SiteController::class, 'store'])->name('api.sites.store');
        Route::post('/assign-site', [SiteController::class, 'assignSite'])->name('api.assign-site');
        
        // Verification management
        Route::post('/request-verification', [VerificationController::class, 'requestVerification'])->name('api.request-verification');
        Route::get('/verifications', [VerificationController::class, 'index'])->name('api.verifications.index');
        
    });
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => [
            'en' => 'API endpoint not found',
            'fr' => 'Point de terminaison API non trouvé',
            'ar' => 'نقطة نهاية API غير موجودة'
        ]
    ], 404);
});
