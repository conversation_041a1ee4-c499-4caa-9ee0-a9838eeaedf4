@echo off
setlocal enabledelayedexpansion

:MENU
cls
echo ========================================
echo     ClockIn API - Gestionnaire
echo ========================================
echo.
echo 1. Demarrer le serveur
echo 2. Ouvrir la documentation
echo 3. Ouvrir l'API dans le navigateur
echo 4. Regenerer la documentation
echo 5. Voir les logs
echo 6. Tester la connexion
echo 7. Arreter le serveur
echo 8. Quitter
echo.
set /p choice="Choisissez une option (1-8): "

if "%choice%"=="1" goto START_SERVER
if "%choice%"=="2" goto OPEN_DOCS
if "%choice%"=="3" goto OPEN_API
if "%choice%"=="4" goto REGEN_DOCS
if "%choice%"=="5" goto VIEW_LOGS
if "%choice%"=="6" goto TEST_CONNECTION
if "%choice%"=="7" goto STOP_SERVER
if "%choice%"=="8" goto EXIT

echo Option invalide. Appuyez sur une touche pour continuer...
pause >nul
goto MENU

:START_SERVER
echo.
echo Demarrage du serveur ClockIn...
echo.
start cmd /k "php artisan serve --host=localhost --port=8081"
echo Serveur demarre dans une nouvelle fenetre.
echo.
pause
goto MENU

:OPEN_DOCS
echo.
echo Ouverture de la documentation...
start http://localhost:8081/docs
echo.
pause
goto MENU

:OPEN_API
echo.
echo Ouverture de l'API...
start http://localhost:8081/api
echo.
pause
goto MENU

:REGEN_DOCS
echo.
echo Regeneration de la documentation...
php artisan scribe:generate
echo Documentation regeneree avec succes !
echo.
pause
goto MENU

:VIEW_LOGS
echo.
echo Affichage des logs Laravel...
echo.
if exist storage\logs\laravel.log (
    type storage\logs\laravel.log | more
) else (
    echo Aucun fichier de log trouve.
)
echo.
pause
goto MENU

:TEST_CONNECTION
echo.
echo Test de connexion a l'API...
echo.
curl -X POST http://localhost:8081/api/login -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\"}"
echo.
echo.
pause
goto MENU

:STOP_SERVER
echo.
echo Arret du serveur...
taskkill /F /IM php.exe >nul 2>&1
echo Serveur arrete.
echo.
pause
goto MENU

:EXIT
echo.
echo Au revoir !
exit /b 0
