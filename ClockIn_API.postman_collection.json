{"info": {"_postman_id": "clockin-api-collection", "name": "ClockIn API", "description": "Collection complète pour tester l'API ClockIn", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "<PERSON><PERSON> Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}}}]}, {"name": "Pointage", "item": [{"name": "Check Location", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": 1,\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/check-location", "host": ["{{base_url}}"], "path": ["check-location"]}}}, {"name": "Save Pointage", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2,\n    \"site_id\": 1,\n    \"debut_pointage\": \"2024-01-15 08:00:00\",\n    \"fin_pointage\": \"2024-01-15 17:00:00\",\n    \"debut_latitude\": 33.5731,\n    \"debut_longitude\": -7.5898,\n    \"fin_latitude\": 33.5731,\n    \"fin_longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/save-pointage", "host": ["{{base_url}}"], "path": ["save-pointage"]}}}, {"name": "List Pointages (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/pointages?per_page=10", "host": ["{{base_url}}"], "path": ["pointages"], "query": [{"key": "per_page", "value": "10"}, {"key": "date_from", "value": "2024-01-01", "disabled": true}, {"key": "user_id", "value": "2", "disabled": true}]}}}]}, {"name": "Employees (Admin)", "item": [{"name": "List Employees", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"], "query": [{"key": "search", "value": "ahmed", "disabled": true}, {"key": "role", "value": "employee", "disabled": true}]}}}, {"name": "Create Employee", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Nouveau Employé\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"]}}}, {"name": "Get Employee", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/employees/2", "host": ["{{base_url}}"], "path": ["employees", "2"]}}}, {"name": "Update Employee", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/employees/2", "host": ["{{base_url}}"], "path": ["employees", "2"]}}}, {"name": "Delete Employee", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/employees/5", "host": ["{{base_url}}"], "path": ["employees", "5"]}}}]}, {"name": "Sites", "item": [{"name": "List Sites (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/sites", "host": ["{{base_url}}"], "path": ["sites"]}}}, {"name": "Create Site (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Nouveau Chantier Test\",\n    \"latitude\": 34.0209,\n    \"longitude\": -6.8416\n}"}, "url": {"raw": "{{base_url}}/sites", "host": ["{{base_url}}"], "path": ["sites"]}}}, {"name": "Assign Site (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": 1,\n    \"user_ids\": [2, 3]\n}"}, "url": {"raw": "{{base_url}}/assign-site", "host": ["{{base_url}}"], "path": ["assign-site"]}}}, {"name": "My Sites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/my-sites", "host": ["{{base_url}}"], "path": ["my-sites"]}}}]}, {"name": "Verification", "item": [{"name": "Request Verification (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2\n}"}, "url": {"raw": "{{base_url}}/request-verification", "host": ["{{base_url}}"], "path": ["request-verification"]}}}, {"name": "Verify Location", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2,\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898,\n    \"date_heure\": \"2024-01-15 14:35:00\"\n}"}, "url": {"raw": "{{base_url}}/verify-location", "host": ["{{base_url}}"], "path": ["verify-location"]}}}, {"name": "List Verifications (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/verifications", "host": ["{{base_url}}"], "path": ["verifications"], "query": [{"key": "user_id", "value": "2", "disabled": true}, {"key": "date_from", "value": "2024-01-01", "disabled": true}]}}}]}]}