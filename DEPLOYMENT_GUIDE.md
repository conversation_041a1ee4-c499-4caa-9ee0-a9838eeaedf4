# Guide de Déploiement - ClockIn API

## Déploiement en Production

### Prérequis Serveur

- **Serveur Web** : Apache 2.4+ ou Nginx 1.18+
- **PHP** : 8.2+ avec extensions :
  - BCMath
  - Ctype
  - Fileinfo
  - JSON
  - Mbstring
  - OpenSSL
  - PDO
  - Tokenizer
  - XML
  - cURL
  - GD
- **Base de données** : MySQL 8.0+ ou MariaDB 10.3+
- **Composer** : 2.0+
- **SSL Certificate** : Recommandé pour HTTPS

### Étapes de Déploiement

#### 1. Préparation du Serveur

```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation de PHP et extensions
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-xml php8.2-curl php8.2-mbstring php8.2-zip php8.2-gd php8.2-bcmath

# Installation de Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Installation de MySQL
sudo apt install mysql-server
```

#### 2. Configuration de la Base de Données

```sql
-- Connexion à MySQL
mysql -u root -p

-- Création de la base de données
CREATE DATABASE clockin_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Création de l'utilisateur
CREATE USER 'clockin_user'@'localhost' IDENTIFIED BY 'mot_de_passe_securise';

-- Attribution des privilèges
GRANT ALL PRIVILEGES ON clockin_production.* TO 'clockin_user'@'localhost';
FLUSH PRIVILEGES;

EXIT;
```

#### 3. Déploiement du Code

```bash
# Clonage du repository
cd /var/www
sudo git clone https://github.com/votre-repo/clockin.git
sudo chown -R www-data:www-data clockin
cd clockin

# Installation des dépendances
composer install --no-dev --optimize-autoloader

# Configuration de l'environnement
sudo cp .env.production.example .env
sudo nano .env  # Modifier avec vos paramètres

# Génération de la clé d'application
php artisan key:generate

# Exécution des migrations
php artisan migrate --force

# Exécution des seeders (optionnel pour la production)
php artisan db:seed --class=AdminSeeder

# Optimisation pour la production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Publication des assets Sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

#### 4. Configuration Apache

Créer le fichier `/etc/apache2/sites-available/clockin.conf` :

```apache
<VirtualHost *:80>
    ServerName api.votre-domaine.com
    DocumentRoot /var/www/clockin/public
    
    <Directory /var/www/clockin/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/clockin_error.log
    CustomLog ${APACHE_LOG_DIR}/clockin_access.log combined
    
    # Redirection HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName api.votre-domaine.com
    DocumentRoot /var/www/clockin/public
    
    <Directory /var/www/clockin/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Configuration SSL
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    SSLCertificateChainFile /path/to/ca_bundle.crt
    
    # Headers de sécurité
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    ErrorLog ${APACHE_LOG_DIR}/clockin_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/clockin_ssl_access.log combined
</VirtualHost>
```

Activer le site :
```bash
sudo a2ensite clockin.conf
sudo a2enmod rewrite ssl headers
sudo systemctl reload apache2
```

#### 5. Configuration Nginx (Alternative)

Créer le fichier `/etc/nginx/sites-available/clockin` :

```nginx
server {
    listen 80;
    server_name api.votre-domaine.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.votre-domaine.com;
    root /var/www/clockin/public;
    index index.php;

    # Configuration SSL
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Headers de sécurité
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Configuration PHP
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Logs
    access_log /var/log/nginx/clockin_access.log;
    error_log /var/log/nginx/clockin_error.log;
}
```

Activer le site :
```bash
sudo ln -s /etc/nginx/sites-available/clockin /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 6. Configuration des Permissions

```bash
# Permissions des fichiers
sudo chown -R www-data:www-data /var/www/clockin
sudo chmod -R 755 /var/www/clockin
sudo chmod -R 775 /var/www/clockin/storage
sudo chmod -R 775 /var/www/clockin/bootstrap/cache
```

#### 7. Configuration des Tâches Cron

Ajouter au crontab de www-data :
```bash
sudo crontab -u www-data -e

# Ajouter cette ligne :
* * * * * cd /var/www/clockin && php artisan schedule:run >> /dev/null 2>&1
```

#### 8. Configuration du Firewall

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Ou iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

### Monitoring et Maintenance

#### 1. Logs

Les logs sont disponibles dans :
- `/var/www/clockin/storage/logs/laravel.log`
- `/var/log/apache2/clockin_*.log` (Apache)
- `/var/log/nginx/clockin_*.log` (Nginx)

#### 2. Sauvegarde de la Base de Données

Script de sauvegarde automatique :
```bash
#!/bin/bash
# /usr/local/bin/backup_clockin.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/clockin"
DB_NAME="clockin_production"
DB_USER="clockin_user"
DB_PASS="mot_de_passe_securise"

mkdir -p $BACKUP_DIR

mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/clockin_$DATE.sql

# Garder seulement les 7 dernières sauvegardes
find $BACKUP_DIR -name "clockin_*.sql" -mtime +7 -delete

echo "Sauvegarde terminée : clockin_$DATE.sql"
```

Ajouter au crontab :
```bash
# Sauvegarde quotidienne à 2h du matin
0 2 * * * /usr/local/bin/backup_clockin.sh
```

#### 3. Mise à Jour

Script de mise à jour :
```bash
#!/bin/bash
# /usr/local/bin/update_clockin.sh

cd /var/www/clockin

# Sauvegarde avant mise à jour
/usr/local/bin/backup_clockin.sh

# Mise en mode maintenance
php artisan down

# Mise à jour du code
git pull origin main

# Mise à jour des dépendances
composer install --no-dev --optimize-autoloader

# Exécution des migrations
php artisan migrate --force

# Nettoyage du cache
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Sortie du mode maintenance
php artisan up

echo "Mise à jour terminée"
```

### Sécurité

#### 1. Configuration SSL/TLS

- Utiliser des certificats SSL valides (Let's Encrypt recommandé)
- Configurer HSTS (HTTP Strict Transport Security)
- Désactiver les protocoles SSL/TLS obsolètes

#### 2. Configuration de la Base de Données

- Utiliser des mots de passe forts
- Limiter les connexions à localhost uniquement
- Activer les logs de requêtes lentes
- Configurer des sauvegardes régulières

#### 3. Configuration du Serveur

- Désactiver l'affichage des erreurs PHP en production
- Configurer fail2ban pour la protection contre les attaques par force brute
- Mettre à jour régulièrement le système et les dépendances

### Troubleshooting

#### Problèmes Courants

1. **Erreur 500** : Vérifier les logs Apache/Nginx et Laravel
2. **Problèmes de permissions** : Vérifier les permissions des dossiers storage et cache
3. **Erreurs de base de données** : Vérifier la configuration dans .env
4. **Problèmes CORS** : Vérifier la configuration dans config/cors.php

#### Commandes Utiles

```bash
# Vérifier les logs en temps réel
tail -f /var/www/clockin/storage/logs/laravel.log

# Nettoyer le cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Vérifier la configuration
php artisan config:show

# Tester la connectivité à la base de données
php artisan tinker
>>> DB::connection()->getPdo();
```

Ce guide couvre les aspects essentiels du déploiement en production. Adaptez les configurations selon votre environnement spécifique.
