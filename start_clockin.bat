@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     ClockIn API - Demarrage Automatique
echo ========================================
echo.

:: Verification de l'installation
if not exist .env (
    echo Erreur: Fichier .env non trouve
    echo Executez d'abord install.bat
    pause
    exit /b 1
)

:: Recherche d'un port disponible
set "PORT=8080"
set "FOUND=0"

for %%p in (8080 8081 8082 8083 8084 8085) do (
    if !FOUND! equ 0 (
        netstat -an | findstr :%%p >nul 2>&1
        if errorlevel 1 (
            set "PORT=%%p"
            set "FOUND=1"
            echo Port disponible trouve: %%p
        )
    )
)

if !FOUND! equ 0 (
    echo Aucun port disponible trouve dans la plage 8080-8085
    echo Utilisation du port 8080 par defaut
    set "PORT=8080"
)

echo.
echo Configuration du port !PORT!...

:: Mise a jour du fichier .env
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=http://localhost:!PORT!' | Set-Content .env"

:: Mise a jour de la configuration Scribe
powershell -Command "(Get-Content config/scribe.php) -replace 'http://localhost:\d+', 'http://localhost:!PORT!' | Set-Content config/scribe.php"

:: Mise a jour de la collection Postman
powershell -Command "(Get-Content ClockIn_API.postman_collection.json) -replace 'http://localhost:\d+/api', 'http://localhost:!PORT!/api' | Set-Content ClockIn_API.postman_collection.json"

echo Configuration mise a jour pour le port !PORT!
echo.

:: Generation de la documentation
echo Generation de la documentation...
php artisan clockin:generate-docs --force >nul 2>&1
if errorlevel 1 (
    echo Attention: Erreur lors de la generation de la documentation
    echo Tentative avec la commande standard...
    php artisan scribe:generate >nul 2>&1
)

echo.
echo ========================================
echo     Demarrage du serveur Laravel
echo ========================================
echo.
echo API accessible sur : http://localhost:!PORT!/api
echo Documentation sur : http://localhost:!PORT!/docs
echo.
echo Le navigateur va s'ouvrir automatiquement...
echo Appuyez sur Ctrl+C pour arreter le serveur
echo.

:: Demarrage du serveur en arriere-plan
start /B php artisan serve --host=localhost --port=!PORT!

:: Attendre que le serveur soit pret
echo Attente du demarrage du serveur...
timeout /t 3 /nobreak >nul

:: Verification que le serveur est pret
:CHECK_SERVER
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:!PORT!' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto CHECK_SERVER
)

echo Serveur pret !
echo.

:: Ouverture automatique de la documentation
echo Ouverture de la documentation dans le navigateur...
start http://localhost:!PORT!/docs

:: Affichage des informations
echo.
echo ========================================
echo     ClockIn API Demarre avec Succes !
echo ========================================
echo.
echo URLs disponibles :
echo - API Base : http://localhost:!PORT!/api
echo - Documentation : http://localhost:!PORT!/docs
echo - Application : http://localhost:!PORT!
echo.
echo Comptes de test :
echo - Admin : <EMAIL> / password123
echo - Employe : <EMAIL> / password123
echo.
echo Endpoints principaux :
echo - POST /api/login - Connexion
echo - POST /api/check-location - Verifier position
echo - POST /api/save-pointage - Enregistrer pointage
echo - GET /api/employees - Liste employes (Admin)
echo.
echo Appuyez sur une touche pour arreter le serveur...
pause >nul

:: Arret du serveur
echo.
echo Arret du serveur...
taskkill /F /IM php.exe >nul 2>&1
echo Serveur arrete.
pause
