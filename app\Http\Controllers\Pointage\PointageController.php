<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckLocationRequest;
use App\Http\Requests\PointageRequest;
use App\Http\Resources\PointageResource;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PointageController extends Controller
{
    /**
     * Check if location is within site range
     */
    public function checkLocation(CheckLocationRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $site = Site::findOrFail($validated['site_id']);
        
        $isWithinRange = $site->isWithinRange(
            $validated['latitude'],
            $validated['longitude']
        );
        
        // Log location check
        Log::createLog(
            auth()->id(),
            'location_check',
            $isWithinRange ? 'success' : 'failed',
            [
                'site_id' => $validated['site_id'],
                'latitude' => $validated['latitude'],
                'longitude' => $validated['longitude'],
                'is_within_range' => $isWithinRange
            ]
        );
        
        return response()->json([
            'success' => true,
            'data' => [
                'is_within_range' => $isWithinRange,
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'latitude' => (float) $site->latitude,
                    'longitude' => (float) $site->longitude
                ]
            ],
            'message' => [
                'en' => $isWithinRange ? 'Location is within range' : 'Location is outside range',
                'fr' => $isWithinRange ? 'Position dans la zone' : 'Position hors zone',
                'ar' => $isWithinRange ? 'الموقع ضمن النطاق' : 'الموقع خارج النطاق'
            ]
        ]);
    }
    
    /**
     * Save pointage
     */
    public function savePointage(PointageRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        try {
            $pointage = Pointage::create($validated);
            
            // Log successful pointage
            Log::createLog(
                auth()->id(),
                'pointage_attempt',
                'success',
                ['pointage_id' => $pointage->id]
            );
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Pointage saved successfully',
                    'fr' => 'Pointage enregistré avec succès',
                    'ar' => 'تم حفظ التوقيت بنجاح'
                ],
                'data' => new PointageResource($pointage->load(['user', 'site']))
            ], 201);
            
        } catch (\Exception $e) {
            // Log failed pointage
            Log::createLog(
                auth()->id(),
                'pointage_attempt',
                'failed',
                ['error' => $e->getMessage()]
            );
            
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to save pointage',
                    'fr' => 'Échec de l\'enregistrement du pointage',
                    'ar' => 'فشل في حفظ التوقيت'
                ]
            ], 500);
        }
    }

    /**
     * Get pointages list (Admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Pointage::with(['user', 'site']);

        // Filter by date
        if ($request->has('date_from')) {
            $query->whereDate('debut_pointage', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('debut_pointage', '<=', $request->date_to);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by site
        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Order by latest first
        $query->orderBy('debut_pointage', 'desc');

        // Paginate results
        $pointages = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => PointageResource::collection($pointages->items()),
            'pagination' => [
                'current_page' => $pointages->currentPage(),
                'last_page' => $pointages->lastPage(),
                'per_page' => $pointages->perPage(),
                'total' => $pointages->total(),
                'from' => $pointages->firstItem(),
                'to' => $pointages->lastItem(),
            ]
        ]);
    }
}
