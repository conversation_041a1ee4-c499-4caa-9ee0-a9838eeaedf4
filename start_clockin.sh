#!/bin/bash

echo "========================================"
echo "     ClockIn API - Démarrage Automatique"
echo "========================================"
echo

# Vérification de l'installation
if [ ! -f .env ]; then
    echo "Erreur: Fichier .env non trouvé"
    echo "Exécutez d'abord install.sh"
    exit 1
fi

# Fonction pour vérifier si un port est disponible
check_port() {
    local port=$1
    if command -v lsof >/dev/null 2>&1; then
        ! lsof -i :$port >/dev/null 2>&1
    elif command -v netstat >/dev/null 2>&1; then
        ! netstat -ln | grep ":$port " >/dev/null 2>&1
    else
        # Fallback: essayer de se connecter au port
        ! timeout 1 bash -c "echo >/dev/tcp/localhost/$port" >/dev/null 2>&1
    fi
}

# Recherche d'un port disponible
PORT=8080
FOUND=0

for p in 8080 8081 8082 8083 8084 8085; do
    if [ $FOUND -eq 0 ]; then
        if check_port $p; then
            PORT=$p
            FOUND=1
            echo "Port disponible trouvé: $p"
        fi
    fi
done

if [ $FOUND -eq 0 ]; then
    echo "Aucun port disponible trouvé dans la plage 8080-8085"
    echo "Utilisation du port 8080 par défaut"
    PORT=8080
fi

echo
echo "Configuration du port $PORT..."

# Mise à jour du fichier .env
sed -i.bak "s|APP_URL=.*|APP_URL=http://localhost:$PORT|" .env

# Mise à jour de la configuration Scribe
sed -i.bak "s|http://localhost:[0-9]*|http://localhost:$PORT|g" config/scribe.php

# Mise à jour de la collection Postman
sed -i.bak "s|http://localhost:[0-9]*/api|http://localhost:$PORT/api|g" ClockIn_API.postman_collection.json

echo "Configuration mise à jour pour le port $PORT"
echo

# Génération de la documentation
echo "Génération de la documentation..."
if ! php artisan clockin:generate-docs --force >/dev/null 2>&1; then
    echo "Attention: Erreur lors de la génération de la documentation"
    echo "Tentative avec la commande standard..."
    php artisan scribe:generate >/dev/null 2>&1
fi

echo
echo "========================================"
echo "     Démarrage du serveur Laravel"
echo "========================================"
echo
echo "API accessible sur : http://localhost:$PORT/api"
echo "Documentation sur : http://localhost:$PORT/docs"
echo
echo "Le navigateur va s'ouvrir automatiquement..."
echo "Appuyez sur Ctrl+C pour arrêter le serveur"
echo

# Démarrage du serveur en arrière-plan
php artisan serve --host=localhost --port=$PORT &
SERVER_PID=$!

# Attendre que le serveur soit prêt
echo "Attente du démarrage du serveur..."
sleep 3

# Vérification que le serveur est prêt
while ! curl -s http://localhost:$PORT >/dev/null 2>&1; do
    sleep 1
done

echo "Serveur prêt !"
echo

# Ouverture automatique de la documentation
echo "Ouverture de la documentation dans le navigateur..."
if command -v xdg-open >/dev/null 2>&1; then
    xdg-open http://localhost:$PORT/docs
elif command -v open >/dev/null 2>&1; then
    open http://localhost:$PORT/docs
elif command -v start >/dev/null 2>&1; then
    start http://localhost:$PORT/docs
else
    echo "Impossible d'ouvrir automatiquement le navigateur"
    echo "Ouvrez manuellement: http://localhost:$PORT/docs"
fi

# Affichage des informations
echo
echo "========================================"
echo "     ClockIn API Démarré avec Succès !"
echo "========================================"
echo
echo "URLs disponibles :"
echo "- API Base : http://localhost:$PORT/api"
echo "- Documentation : http://localhost:$PORT/docs"
echo "- Application : http://localhost:$PORT"
echo
echo "Comptes de test :"
echo "- Admin : <EMAIL> / password123"
echo "- Employé : <EMAIL> / password123"
echo
echo "Endpoints principaux :"
echo "- POST /api/login - Connexion"
echo "- POST /api/check-location - Vérifier position"
echo "- POST /api/save-pointage - Enregistrer pointage"
echo "- GET /api/employees - Liste employés (Admin)"
echo
echo "Appuyez sur Ctrl+C pour arrêter le serveur..."

# Attendre l'arrêt
trap "echo; echo 'Arrêt du serveur...'; kill $SERVER_PID 2>/dev/null; echo 'Serveur arrêté.'; exit 0" INT

wait $SERVER_PID
