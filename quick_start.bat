@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     ClockIn API - Demarrage Rapide
echo ========================================
echo.

:: Verification si deja installe
if not exist .env (
    echo Installation de ClockIn en cours...
    echo.
    
    :: Installation des dependances
    echo 1. Installation des dependances Composer...
    composer install --quiet
    if errorlevel 1 (
        echo Erreur lors de l'installation des dependances
        pause
        exit /b 1
    )
    
    :: Configuration
    echo 2. Configuration de l'environnement...
    copy .env.example .env >nul
    php artisan key:generate --quiet
    
    :: Configuration de la base de donnees
    echo.
    echo Configuration de la base de donnees:
    echo Assurez-vous que MySQL est demarre et que la base 'clockin_db' existe
    echo.
    set /p continue="Continuer avec les migrations ? (o/n): "
    if /i "!continue!" neq "o" (
        echo Installation interrompue.
        pause
        exit /b 0
    )
    
    echo 3. Execution des migrations...
    php artisan migrate --force --quiet
    if errorlevel 1 (
        echo Erreur lors des migrations
        echo Verifiez votre configuration de base de donnees dans .env
        pause
        exit /b 1
    )
    
    echo 4. Execution des seeders...
    php artisan db:seed --quiet
    if errorlevel 1 (
        echo Erreur lors du seeding
        pause
        exit /b 1
    )
    
    echo.
    echo Installation terminee !
    echo.
)

:: Recherche d'un port disponible
set "PORT=8080"
set "FOUND=0"

echo Recherche d'un port disponible...
for %%p in (8080 8081 8082 8083 8084 8085) do (
    if !FOUND! equ 0 (
        netstat -an | findstr :%%p >nul 2>&1
        if errorlevel 1 (
            set "PORT=%%p"
            set "FOUND=1"
            echo Port !PORT! disponible
        )
    )
)

:: Configuration automatique du port
echo Configuration du port !PORT!...
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=http://localhost:!PORT!' | Set-Content .env" >nul 2>&1

:: Installation et configuration de Scribe si necessaire
if not exist config\scribe.php (
    echo Installation de Scribe...
    composer require --dev knuckleswtf/scribe --quiet
    php artisan vendor:publish --tag=scribe-config --force --quiet
)

:: Mise a jour de la configuration Scribe
powershell -Command "(Get-Content config/scribe.php) -replace 'http://localhost:\d+', 'http://localhost:!PORT!' | Set-Content config/scribe.php" >nul 2>&1

:: Generation de la documentation
echo Generation de la documentation...
php artisan scribe:generate --quiet
if errorlevel 1 (
    echo Attention: Erreur lors de la generation de la documentation
)

echo.
echo ========================================
echo     Demarrage de ClockIn API
echo ========================================
echo.
echo Port utilise: !PORT!
echo API: http://localhost:!PORT!/api
echo Documentation: http://localhost:!PORT!/docs
echo.

:: Demarrage du serveur
start /B php artisan serve --host=localhost --port=!PORT!

:: Attente du serveur
echo Demarrage du serveur...
timeout /t 5 /nobreak >nul

:: Verification et ouverture
:CHECK_SERVER
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:!PORT!' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto CHECK_SERVER
)

echo.
echo ========================================
echo     ClockIn API Pret !
echo ========================================
echo.

:: Ouverture automatique
echo Ouverture de la documentation...
start http://localhost:!PORT!/docs

echo.
echo Informations importantes:
echo.
echo URLs:
echo - API Base: http://localhost:!PORT!/api
echo - Documentation: http://localhost:!PORT!/docs
echo.
echo Comptes de test:
echo - Admin: <EMAIL> / password123
echo - Employes: <EMAIL> / password123
echo.
echo Tests rapides:
echo 1. Ouvrez la documentation dans votre navigateur
echo 2. Testez la connexion avec "Try It Out"
echo 3. Importez la collection Postman: ClockIn_API.postman_collection.json
echo.
echo Appuyez sur une touche pour arreter le serveur...
pause >nul

:: Arret
echo Arret du serveur...
taskkill /F /IM php.exe >nul 2>&1
echo Serveur arrete.
pause
