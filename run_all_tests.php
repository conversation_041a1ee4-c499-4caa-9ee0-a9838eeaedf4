<?php

echo "===============================================\n";
echo "    TESTS COMPLETS CLOCKIN API & DATABASE\n";
echo "===============================================\n\n";

$startTime = microtime(true);

// Fonction pour exécuter une commande et capturer la sortie
function runCommand($command) {
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    return [
        'output' => implode("\n", $output),
        'success' => $returnCode === 0,
        'code' => $returnCode
    ];
}

// Fonction pour vérifier si un service est accessible
function checkService($url, $timeout = 5) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return !$error && $httpCode < 400;
}

echo "ÉTAPE 1: VÉRIFICATION DE L'ENVIRONNEMENT\n";
echo "=========================================\n";

// Vérifier PHP
$phpVersion = PHP_VERSION;
echo "✅ PHP Version: $phpVersion\n";

// Vérifier les fichiers essentiels
$essentialFiles = ['.env', 'artisan', 'composer.json'];
foreach ($essentialFiles as $file) {
    if (file_exists($file)) {
        echo "✅ Fichier $file trouvé\n";
    } else {
        echo "❌ Fichier $file manquant\n";
    }
}

// Vérifier la configuration .env
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'DB_DATABASE=clockin_db') !== false) {
        echo "✅ Configuration base de données trouvée\n";
    } else {
        echo "⚠️  Configuration base de données à vérifier\n";
    }
}

echo "\nÉTAPE 2: VÉRIFICATION DES SERVICES\n";
echo "===================================\n";

// Vérifier phpMyAdmin
if (checkService('http://localhost:8080/phpmyadmin')) {
    echo "✅ phpMyAdmin accessible (http://localhost:8080/phpmyadmin)\n";
} else {
    echo "❌ phpMyAdmin non accessible - WampServer démarré ?\n";
}

// Vérifier si le serveur Laravel est démarré
if (checkService('http://localhost:8001')) {
    echo "✅ Serveur Laravel accessible (http://localhost:8001)\n";
    $laravelRunning = true;
} else {
    echo "⚠️  Serveur Laravel non accessible - démarrage automatique...\n";
    $laravelRunning = false;
    
    // Essayer de démarrer le serveur Laravel en arrière-plan
    if (PHP_OS_FAMILY === 'Windows') {
        pclose(popen('start /B php artisan serve --port=8001', 'r'));
    } else {
        pclose(popen('php artisan serve --port=8001 > /dev/null 2>&1 &', 'r'));
    }
    
    // Attendre un peu et revérifier
    sleep(3);
    if (checkService('http://localhost:8001')) {
        echo "✅ Serveur Laravel démarré avec succès\n";
        $laravelRunning = true;
    } else {
        echo "❌ Impossible de démarrer le serveur Laravel\n";
    }
}

echo "\nÉTAPE 3: TEST DE CONNECTIVITÉ BASE DE DONNÉES\n";
echo "==============================================\n";

// Exécuter le test de base de données
echo "Exécution du test de connectivité...\n";
$result = runCommand('php test_all_apis.php');

if ($result['success']) {
    echo "✅ Test de base de données réussi\n";
    echo "Extrait de la sortie:\n";
    $lines = explode("\n", $result['output']);
    foreach ($lines as $line) {
        if (strpos($line, '✅') !== false || strpos($line, '❌') !== false) {
            echo "   $line\n";
        }
    }
} else {
    echo "❌ Test de base de données échoué\n";
    echo "Erreur: " . substr($result['output'], 0, 500) . "...\n";
}

if ($laravelRunning) {
    echo "\nÉTAPE 4: TEST DES ENDPOINTS API\n";
    echo "===============================\n";
    
    // Attendre que le serveur soit complètement démarré
    sleep(2);
    
    echo "Exécution des tests d'API...\n";
    $apiResult = runCommand('php test_api_endpoints.php');
    
    if ($apiResult['success']) {
        echo "✅ Tests d'API exécutés\n";
        
        // Extraire les résultats importants
        $lines = explode("\n", $apiResult['output']);
        $inSummary = false;
        foreach ($lines as $line) {
            if (strpos($line, 'RÉSUMÉ DES TESTS') !== false) {
                $inSummary = true;
            }
            if ($inSummary || strpos($line, '✅') !== false || strpos($line, '❌') !== false || strpos($line, '🎉') !== false) {
                echo "   $line\n";
            }
        }
    } else {
        echo "❌ Tests d'API échoués\n";
        echo "Erreur: " . substr($apiResult['output'], 0, 500) . "...\n";
    }
} else {
    echo "\nÉTAPE 4: TESTS API IGNORÉS\n";
    echo "===========================\n";
    echo "⚠️  Serveur Laravel non disponible - tests API ignorés\n";
}

echo "\nÉTAPE 5: TESTS LARAVEL NATIFS\n";
echo "==============================\n";

// Exécuter les tests Laravel si possible
echo "Tentative d'exécution des tests Laravel...\n";
$laravelTestResult = runCommand('php artisan test --stop-on-failure');

if ($laravelTestResult['success']) {
    echo "✅ Tests Laravel réussis\n";
    
    // Extraire le résumé
    $lines = explode("\n", $laravelTestResult['output']);
    foreach ($lines as $line) {
        if (strpos($line, 'Tests:') !== false || strpos($line, 'PASS') !== false || strpos($line, 'FAIL') !== false) {
            echo "   $line\n";
        }
    }
} else {
    echo "⚠️  Tests Laravel échoués ou non exécutables\n";
    echo "Cela peut être normal si la base de données n'est pas configurée\n";
}

echo "\nÉTAPE 6: RÉSUMÉ GLOBAL\n";
echo "======================\n";

$endTime = microtime(true);
$duration = round($endTime - $startTime, 2);

echo "Durée totale des tests: {$duration}s\n\n";

// Calculer le score global
$totalChecks = 0;
$passedChecks = 0;

// Vérifications de base
$totalChecks += 4; // PHP, fichiers, config, services
$passedChecks += 1; // PHP toujours OK
if (file_exists('.env') && file_exists('artisan')) $passedChecks++;
if (file_exists('.env') && strpos(file_get_contents('.env'), 'clockin_db') !== false) $passedChecks++;
if (checkService('http://localhost:8080/phpmyadmin')) $passedChecks++;

// Tests de base de données
$totalChecks++;
if (isset($result) && $result['success']) $passedChecks++;

// Tests API
if ($laravelRunning) {
    $totalChecks++;
    if (isset($apiResult) && $apiResult['success']) $passedChecks++;
}

echo "Score global: $passedChecks/$totalChecks vérifications réussies\n";
$percentage = round(($passedChecks / $totalChecks) * 100, 1);
echo "Pourcentage de réussite: $percentage%\n\n";

if ($percentage >= 80) {
    echo "🎉 EXCELLENT ! Le système ClockIn fonctionne correctement\n";
    echo "✅ Base de données opérationnelle\n";
    echo "✅ APIs fonctionnelles\n";
    echo "✅ Prêt pour la production\n";
} elseif ($percentage >= 60) {
    echo "⚠️  BON - Quelques problèmes mineurs à corriger\n";
    echo "La plupart des fonctionnalités sont opérationnelles\n";
} else {
    echo "❌ PROBLÈMES DÉTECTÉS - Configuration requise\n";
    echo "Plusieurs éléments nécessitent une attention\n";
}

echo "\n💡 ACCÈS RAPIDE:\n";
echo "- phpMyAdmin: http://localhost:8080/phpmyadmin\n";
echo "- API ClockIn: http://localhost:8001/api\n";
echo "- Documentation: http://localhost:8001/docs\n";
echo "- Base de données: clockin_db\n";

echo "\n📋 ACTIONS RECOMMANDÉES:\n";
if (!checkService('http://localhost:8080/phpmyadmin')) {
    echo "1. Démarrer WampServer\n";
}
if (!file_exists('.env') || strpos(file_get_contents('.env'), 'APP_KEY=base64:') === false) {
    echo "2. Configurer .env et générer la clé: php artisan key:generate\n";
}
if (!$laravelRunning) {
    echo "3. Démarrer le serveur Laravel: php artisan serve --port=8001\n";
}
echo "4. Exécuter les migrations: php artisan migrate --seed\n";
echo "5. Tester avec Postman: ClockIn_API.postman_collection.json\n";

echo "\n===============================================\n";
echo "    FIN DES TESTS COMPLETS\n";
echo "===============================================\n";
