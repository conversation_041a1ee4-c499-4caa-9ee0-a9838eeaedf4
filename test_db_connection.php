<?php

echo "=== Test de connexion à la base de données ClockIn ===\n\n";

// Configuration de la base de données
$host = '127.0.0.1';
$port = '3306';
$database = 'clockin_db';
$username = 'root';
$password = '';

echo "Configuration:\n";
echo "- Host: $host:$port\n";
echo "- Database: $database\n";
echo "- Username: $username\n";
echo "- Password: " . (empty($password) ? '(vide)' : '***') . "\n\n";

try {
    // Test de connexion MySQL
    echo "1. Test de connexion MySQL...\n";
    $dsn = "mysql:host=$host;port=$port";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connexion MySQL réussie\n\n";
    
    // Test d'existence de la base de données
    echo "2. Vérification de l'existence de la base de données '$database'...\n";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "✅ Base de données '$database' trouvée\n\n";
        
        // Connexion à la base de données spécifique
        echo "3. Connexion à la base de données '$database'...\n";
        $dsn = "mysql:host=$host;port=$port;dbname=$database";
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "✅ Connexion à '$database' réussie\n\n";
        
        // Liste des tables
        echo "4. Liste des tables existantes:\n";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "⚠️  Aucune table trouvée - migrations nécessaires\n";
        } else {
            foreach ($tables as $table) {
                echo "   - $table\n";
            }
        }
        echo "\n";
        
        // Test de requête simple
        echo "5. Test de requête simple...\n";
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result && $result['test'] == 1) {
            echo "✅ Requête de test réussie\n";
        } else {
            echo "❌ Échec de la requête de test\n";
        }
        
    } else {
        echo "❌ Base de données '$database' non trouvée\n";
        echo "📝 Bases de données disponibles:\n";
        $stmt = $pdo->query("SHOW DATABASES");
        $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
        foreach ($databases as $db) {
            echo "   - $db\n";
        }
        echo "\n";
        echo "💡 Créez la base de données avec: CREATE DATABASE $database;\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n\n";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "💡 Suggestions:\n";
        echo "   - Vérifiez que WampServer est démarré\n";
        echo "   - Vérifiez que MySQL est en cours d'exécution\n";
        echo "   - Vérifiez le port MySQL (par défaut 3306)\n";
    } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "💡 Suggestions:\n";
        echo "   - Vérifiez le nom d'utilisateur et mot de passe\n";
        echo "   - Vérifiez les permissions MySQL\n";
    }
}

echo "\n=== Fin du test ===\n";
