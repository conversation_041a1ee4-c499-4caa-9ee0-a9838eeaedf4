<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Unauthorized access',
                    'fr' => 'Accès non autorisé',
                    'ar' => 'وصول غير مصرح به'
                ]
            ], 401);
        }

        if (!auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Admin access required',
                    'fr' => 'Accès administrateur requis',
                    'ar' => 'مطلوب وصول المدير'
                ]
            ], 403);
        }

        return $next($request);
    }
}
