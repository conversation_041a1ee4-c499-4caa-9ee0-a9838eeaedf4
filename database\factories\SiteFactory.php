<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Site>
 */
class SiteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company() . ' - ' . fake()->city(),
            'latitude' => fake()->latitude(30, 36), // Morocco latitude range
            'longitude' => fake()->longitude(-12, -1), // Morocco longitude range
        ];
    }
}
