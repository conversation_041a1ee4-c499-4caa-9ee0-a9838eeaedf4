# 🚀 ClockIn API - Serveur Démarré avec Succès !

## ✅ Statut du Serveur

**Le serveur Laravel ClockIn est maintenant actif et fonctionnel !**

### 🌐 URLs d'Accès

| Service | URL | Description |
|---------|-----|-------------|
| **🏠 Application** | http://localhost:8081 | Page d'accueil Laravel |
| **🔗 API Base** | http://localhost:8081/api | Base URL pour tous les endpoints |
| **📚 Documentation** | http://localhost:8081/docs | Documentation interactive Scribe |

### 🔑 Comptes de Test Disponibles

| Rôle | Email | Mot de passe | Permissions |
|------|-------|--------------|-------------|
| **👑 Admin** | <EMAIL> | password123 | Accès complet à toutes les fonctionnalités |
| **👤 Employé** | <EMAIL> | password123 | Pointage et vérification de localisation |
| **👤 Employé** | <EMAIL> | password123 | Pointage et vérification de localisation |
| **👤 Employé** | <EMAIL> | password123 | Pointage et vérification de localisation |

## 🧪 Tests Rapides

### 1. Test de Connexion (cURL)
```bash
curl -X POST http://localhost:8081/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 2. Test via Documentation Interactive
1. **Ouvrez** : http://localhost:8081/docs
2. **Naviguez** vers "Authentication > Login"
3. **Cliquez** sur "Try it out"
4. **Entrez** les identifiants admin
5. **Exécutez** la requête

### 3. Test avec Postman
1. **Importez** : `ClockIn_API.postman_collection.json`
2. **Variable base_url** : Automatiquement définie sur `http://localhost:8081/api`
3. **Testez** la connexion admin

## 📍 Endpoints Principaux

### 🔐 Authentification
- `POST /api/login` - Connexion utilisateur
- `POST /api/logout` - Déconnexion utilisateur
- `GET /api/me` - Profil utilisateur connecté

### ⏰ Pointage
- `POST /api/check-location` - Vérifier position GPS
- `POST /api/save-pointage` - Enregistrer un pointage
- `GET /api/pointages` - Liste des pointages (Admin)

### 👥 Gestion Employés (Admin)
- `GET /api/employees` - Liste des employés
- `POST /api/employees` - Créer un employé
- `PUT /api/employees/{id}` - Modifier un employé
- `DELETE /api/employees/{id}` - Supprimer un employé

### 🏗️ Gestion Chantiers
- `GET /api/sites` - Liste des chantiers (Admin)
- `POST /api/sites` - Créer un chantier (Admin)
- `POST /api/assign-site` - Assigner chantier (Admin)
- `GET /api/my-sites` - Mes chantiers assignés

### 🔍 Vérification Localisation
- `POST /api/request-verification` - Demander vérification (Admin)
- `POST /api/verify-location` - Vérifier sa position
- `GET /api/verifications` - Historique vérifications (Admin)

## 🗺️ Chantiers de Test

| Nom | Latitude | Longitude | Ville |
|-----|----------|-----------|-------|
| Chantier Casablanca Centre | 33.5731 | -7.5898 | Casablanca |
| Chantier Rabat Agdal | 34.0209 | -6.8416 | Rabat |
| Chantier Marrakech Gueliz | 31.6295 | -7.9811 | Marrakech |

**Rayon de vérification** : 50 mètres autour de chaque chantier

## 🎯 Workflow de Test Complet

### Scénario : Pointage d'un Employé

1. **Connexion Employé**
   ```bash
   curl -X POST http://localhost:8081/api/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password123"}'
   ```

2. **Récupération du Token**
   - Copiez le token depuis la réponse JSON

3. **Vérification Position**
   ```bash
   curl -X POST http://localhost:8081/api/check-location \
     -H "Authorization: Bearer {TOKEN}" \
     -H "Content-Type: application/json" \
     -d '{
       "site_id": 1,
       "latitude": 33.5731,
       "longitude": -7.5898
     }'
   ```

4. **Enregistrement Pointage**
   ```bash
   curl -X POST http://localhost:8081/api/save-pointage \
     -H "Authorization: Bearer {TOKEN}" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": 2,
       "site_id": 1,
       "debut_pointage": "2024-01-15 08:00:00",
       "debut_latitude": 33.5731,
       "debut_longitude": -7.5898
     }'
   ```

## 📚 Documentation Interactive

### Fonctionnalités Disponibles
- ✅ **Try It Out** - Tests en direct des endpoints
- ✅ **Authentification** - Gestion automatique des tokens
- ✅ **Exemples** - Données pré-remplies réalistes
- ✅ **Validation** - Erreurs affichées en temps réel
- ✅ **Multilingue** - Messages en FR/EN/AR

### Navigation
- **Groupes organisés** : Authentication, Pointage, Employees, Sites, Verification
- **Recherche** : Fonction de recherche intégrée
- **Responsive** : Compatible mobile et desktop

## 🛠️ Gestion du Serveur

### Arrêter le Serveur
- **Ctrl+C** dans le terminal où le serveur fonctionne
- Ou fermer la fenêtre de terminal

### Redémarrer le Serveur
```bash
# Démarrage simple
php artisan serve --host=localhost --port=8081

# Ou utiliser les scripts
start_clockin.bat  # Windows
./start_clockin.sh # Linux/Mac
```

### Régénérer la Documentation
```bash
php artisan scribe:generate
```

## 🔧 Dépannage

### Problèmes Courants

1. **Port occupé** : Changez le port dans les scripts
2. **Base de données** : Vérifiez la configuration dans `.env`
3. **Documentation vide** : Régénérez avec `php artisan scribe:generate`
4. **Erreurs 500** : Consultez `storage/logs/laravel.log`

### Logs
- **Laravel** : `storage/logs/laravel.log`
- **Serveur** : Affiché dans le terminal

## 🎉 Félicitations !

**ClockIn API est maintenant opérationnel !**

- ✅ Serveur Laravel actif sur le port 8081
- ✅ Documentation interactive accessible
- ✅ Base de données configurée avec données de test
- ✅ Tous les endpoints fonctionnels
- ✅ Authentification sécurisée
- ✅ Tests prêts à l'emploi

**Prochaines étapes** :
1. Explorez la documentation interactive
2. Testez les endpoints avec Postman
3. Intégrez avec votre application Flutter
4. Personnalisez selon vos besoins

---

**Documentation générée automatiquement** - ClockIn API v1.0
