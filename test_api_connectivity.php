<?php

echo "=== Test de connectivité API ClockIn ===\n\n";

$baseUrl = 'http://localhost:8001/api';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// Test 1: Vérifier si le serveur Laravel répond
echo "1. Test de connectivité du serveur Laravel...\n";
$result = makeRequest($baseUrl . '/login', 'POST', [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
]);

if ($result['error']) {
    echo "❌ Erreur de connexion: " . $result['error'] . "\n";
    echo "💡 Vérifiez que le serveur Laravel est démarré sur le port 8001\n";
    exit(1);
} else {
    echo "✅ Serveur Laravel accessible (HTTP " . $result['http_code'] . ")\n";
    if ($result['response']) {
        $response = json_decode($result['response'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ Réponse JSON valide reçue\n";
        }
    }
}

echo "\n";

// Test 2: Test de login avec des identifiants de test
echo "2. Test de login avec identifiants de test...\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$result = makeRequest($baseUrl . '/login', 'POST', $loginData);
echo "Code HTTP: " . $result['http_code'] . "\n";

if ($result['response']) {
    $response = json_decode($result['response'], true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "Réponse: " . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if (isset($response['success']) && $response['success']) {
            echo "✅ Login réussi\n";
            $token = $response['data']['token'] ?? null;
            
            if ($token) {
                echo "✅ Token reçu\n";
                
                // Test 3: Test du profil utilisateur
                echo "\n3. Test du profil utilisateur...\n";
                $profileResult = makeRequest($baseUrl . '/me', 'GET', null, [
                    'Authorization: Bearer ' . $token
                ]);
                
                echo "Code HTTP: " . $profileResult['http_code'] . "\n";
                if ($profileResult['response']) {
                    $profileResponse = json_decode($profileResult['response'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "Profil: " . json_encode($profileResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                        
                        if (isset($profileResponse['success']) && $profileResponse['success']) {
                            echo "✅ Récupération du profil réussie\n";
                        }
                    }
                }
            }
        } else {
            echo "⚠️  Login échoué - probablement pas de données de test\n";
        }
    }
} else {
    echo "❌ Aucune réponse reçue\n";
}

echo "\n=== Fin du test ===\n";
