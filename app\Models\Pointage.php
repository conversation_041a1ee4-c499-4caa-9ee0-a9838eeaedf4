<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Pointage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'site_id',
        'debut_pointage',
        'fin_pointage',
        'duree',
        'debut_latitude',
        'debut_longitude',
        'fin_latitude',
        'fin_longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'debut_pointage' => 'datetime',
        'fin_pointage' => 'datetime',
        'debut_latitude' => 'decimal:8',
        'debut_longitude' => 'decimal:8',
        'fin_latitude' => 'decimal:8',
        'fin_longitude' => 'decimal:8',
    ];

    /**
     * Get the user that owns the pointage
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the site that owns the pointage
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Calculate and set duration when fin_pointage is set
     */
    public function calculateDuration(): void
    {
        if ($this->debut_pointage && $this->fin_pointage) {
            $debut = Carbon::parse($this->debut_pointage);
            $fin = Carbon::parse($this->fin_pointage);
            
            $diffInSeconds = $fin->diffInSeconds($debut);
            $hours = intval($diffInSeconds / 3600);
            $minutes = intval(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;
            
            $this->duree = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    }

    /**
     * Boot method to automatically calculate duration
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($pointage) {
            $pointage->calculateDuration();
        });
    }
}
