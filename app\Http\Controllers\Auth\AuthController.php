<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    /**
     * Login user and create token
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();
        
        $user = User::where('email', $credentials['email'])->first();
        
        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            // Log failed login attempt
            Log::createLog(
                $user?->id,
                'login_attempt',
                'failed',
                ['email' => $credentials['email'], 'reason' => 'invalid_credentials']
            );
            
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Invalid credentials',
                    'fr' => 'Identifiants invalides',
                    'ar' => 'بيانات اعتماد غير صحيحة'
                ]
            ], 401);
        }
        
        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;
        
        // Log successful login
        Log::createLog(
            $user->id,
            'login_attempt',
            'success',
            ['email' => $credentials['email']]
        );
        
        return response()->json([
            'success' => true,
            'message' => [
                'en' => 'Login successful',
                'fr' => 'Connexion réussie',
                'ar' => 'تم تسجيل الدخول بنجاح'
            ],
            'data' => [
                'user' => new UserResource($user),
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ]);
    }
    
    /**
     * Logout user (revoke token)
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();
        
        // Log logout
        Log::createLog(
            $user->id,
            'logout_attempt',
            'success'
        );
        
        return response()->json([
            'success' => true,
            'message' => [
                'en' => 'Logout successful',
                'fr' => 'Déconnexion réussie',
                'ar' => 'تم تسجيل الخروج بنجاح'
            ]
        ]);
    }
    
    /**
     * Get authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new UserResource($request->user())
        ]);
    }
}
