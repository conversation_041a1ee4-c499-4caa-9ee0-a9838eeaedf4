<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class CheckLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'site_id' => 'required|exists:sites,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'site_id.required' => 'Site ID is required / ID du site requis / معرف الموقع مطلوب',
            'site_id.exists' => 'Site not found / Site non trouvé / الموقع غير موجود',
            'latitude.required' => 'Latitude is required / Latitude requise / خط العرض مطلوب',
            'latitude.numeric' => 'Invalid latitude format / Format de latitude invalide / تنسيق خط العرض غير صحيح',
            'latitude.between' => 'Latitude must be between -90 and 90 / La latitude doit être entre -90 et 90 / يجب أن يكون خط العرض بين -90 و 90',
            'longitude.required' => 'Longitude is required / Longitude requise / خط الطول مطلوب',
            'longitude.numeric' => 'Invalid longitude format / Format de longitude invalide / تنسيق خط الطول غير صحيح',
            'longitude.between' => 'Longitude must be between -180 and 180 / La longitude doit être entre -180 et 180 / يجب أن يكون خط الطول بين -180 و 180',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Validation failed',
                    'fr' => 'Échec de la validation',
                    'ar' => 'فشل التحقق'
                ],
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
