<?php

echo "=== Test Simple de Connectivité ClockIn ===\n\n";

// Test 1: Vérifier si nous sommes dans le bon répertoire
echo "1. Vérification du répertoire de travail...\n";
$currentDir = getcwd();
echo "Répertoire actuel: $currentDir\n";

if (file_exists('.env')) {
    echo "✅ Fichier .env trouvé\n";
} else {
    echo "❌ Fichier .env manquant\n";
}

if (file_exists('artisan')) {
    echo "✅ Fichier artisan trouvé\n";
} else {
    echo "❌ Fichier artisan manquant\n";
}

echo "\n";

// Test 2: Vérifier la configuration de la base de données
echo "2. Lecture de la configuration de la base de données...\n";
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        if (strpos($line, 'DB_') === 0) {
            echo "$line\n";
        }
    }
} else {
    echo "❌ Impossible de lire la configuration\n";
}

echo "\n";

// Test 3: Test de connexion PDO direct
echo "3. Test de connexion PDO direct...\n";
try {
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'clockin_db';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion PDO réussie\n";
    
    // Test de requête simple
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    if ($result && $result['test'] == 1) {
        echo "✅ Requête de test réussie\n";
    }
    
    // Liste des tables
    echo "\n4. Tables existantes:\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "⚠️  Aucune table trouvée - migrations nécessaires\n";
    } else {
        foreach ($tables as $table) {
            echo "   - $table\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "\n💡 Suggestions:\n";
        echo "   - Vérifiez que WampServer est démarré\n";
        echo "   - Vérifiez que MySQL est en cours d'exécution\n";
    } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "\n💡 Suggestions:\n";
        echo "   - Vérifiez le nom d'utilisateur et mot de passe\n";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "\n💡 Suggestions:\n";
        echo "   - Créez la base de données 'clockin_db' dans phpMyAdmin\n";
    }
}

echo "\n=== Fin du test ===\n";
