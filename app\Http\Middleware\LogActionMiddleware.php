<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Log;

class LogActionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Log the action after the request is processed
        $this->logAction($request, $response);

        return $response;
    }

    /**
     * Log the action
     */
    private function logAction(Request $request, Response $response): void
    {
        $userId = auth()->id();
        $action = $this->getActionFromRoute($request);
        $status = $response->getStatusCode() >= 200 && $response->getStatusCode() < 300 ? 'success' : 'failed';
        
        $details = [
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'status_code' => $response->getStatusCode(),
        ];

        // Add request data for specific actions
        if (in_array($action, ['pointage_attempt', 'login_attempt'])) {
            $details['request_data'] = $request->except(['password']);
        }

        Log::createLog(
            $userId,
            $action,
            $status,
            $details,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * Get action name from route
     */
    private function getActionFromRoute(Request $request): string
    {
        $routeName = $request->route()?->getName();
        
        return match($routeName) {
            'api.login' => 'login_attempt',
            'api.logout' => 'logout_attempt',
            'api.save-pointage' => 'pointage_attempt',
            'api.check-location' => 'location_check',
            'api.verify-location' => 'location_verification',
            default => 'api_request'
        };
    }
}
